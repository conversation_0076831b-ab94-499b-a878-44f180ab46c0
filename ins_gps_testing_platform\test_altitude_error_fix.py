#!/usr/bin/env python3
"""
Test script to verify the altitude error fix in Pohang results visualization.

This script demonstrates the difference between the old (incorrect) and new (correct)
altitude error calculation approaches.
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_test_data():
    """Create synthetic test data to demonstrate the altitude error issue"""
    
    # Create time series
    time = np.linspace(0, 100, 1000)  # 100 seconds, 1000 points
    
    # Create baseline trajectory (ground truth) - sinusoidal depth variation
    base_north = 100 * np.sin(0.1 * time)
    base_east = 100 * np.cos(0.1 * time)
    base_down = 10 + 2 * np.sin(0.05 * time)  # Depth varies from 8m to 12m
    
    # Create estimated trajectory with some error
    # Add systematic bias and random noise
    est_north = base_north + 0.5 + 0.2 * np.random.randn(len(time))
    est_east = base_east - 0.3 + 0.2 * np.random.randn(len(time))
    est_down = base_down + 0.8 + 0.1 * np.random.randn(len(time))  # Systematic depth bias
    
    return {
        'time': time,
        'base_north': base_north,
        'base_east': base_east,
        'base_down': base_down,
        'est_north': est_north,
        'est_east': est_east,
        'est_down': est_down
    }

def calculate_errors_old_way(data):
    """Calculate altitude error using the old (incorrect) method"""
    
    # Convert down to altitude (old approach)
    est_altitude = -data['est_down']
    base_altitude = -data['base_down']
    altitude_error_old = est_altitude - base_altitude
    
    return altitude_error_old

def calculate_errors_new_way(data):
    """Calculate vertical position error using the new (correct) method"""
    
    # Direct down error calculation (new approach)
    down_error_new = data['est_down'] - data['base_down']
    
    return down_error_new

def calculate_2d_error(data):
    """Calculate 2D positioning error for comparison"""
    
    error_north = data['est_north'] - data['base_north']
    error_east = data['est_east'] - data['base_east']
    error_2d = np.sqrt(error_north**2 + error_east**2)
    
    return error_2d, error_north, error_east

def plot_comparison(data):
    """Create comparison plots showing the difference between old and new methods"""
    
    # Calculate errors
    altitude_error_old = calculate_errors_old_way(data)
    down_error_new = calculate_errors_new_way(data)
    error_2d, error_north, error_east = calculate_2d_error(data)
    
    # Create figure
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Altitude Error Calculation: Old vs New Method', fontsize=16)
    
    # Plot 1: Old method (incorrect)
    ax1 = axes[0, 0]
    ax1.plot(data['time'], altitude_error_old, 'r-', linewidth=2, label='Altitude Error (Old)')
    ax1.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.3)
    ax1.set_title('Old Method: Altitude Error = (-est_down) - (-base_down)')
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Altitude Error (m)')
    ax1.grid(True)
    ax1.legend()
    
    # Add statistics
    rms_old = np.sqrt(np.mean(altitude_error_old**2))
    mean_old = np.mean(altitude_error_old)
    ax1.text(0.02, 0.98, f'RMS: {rms_old:.2f}m\nMean: {mean_old:.2f}m',
             transform=ax1.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
    
    # Plot 2: New method (correct)
    ax2 = axes[0, 1]
    ax2.plot(data['time'], down_error_new, 'g-', linewidth=2, label='Vertical Position Error (New)')
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.3)
    ax2.set_title('New Method: Down Error = est_down - base_down')
    ax2.set_xlabel('Time (s)')
    ax2.set_ylabel('Vertical Position Error (m)')
    ax2.grid(True)
    ax2.legend()
    
    # Add statistics
    rms_new = np.sqrt(np.mean(down_error_new**2))
    mean_new = np.mean(down_error_new)
    ax2.text(0.02, 0.98, f'RMS: {rms_new:.2f}m\nMean: {mean_new:.2f}m',
             transform=ax2.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    # Plot 3: Relationship between methods
    ax3 = axes[1, 0]
    ax3.plot(data['time'], altitude_error_old, 'r-', linewidth=2, label='Old Method', alpha=0.7)
    ax3.plot(data['time'], -down_error_new, 'g--', linewidth=2, label='-(New Method)', alpha=0.7)
    ax3.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.3)
    ax3.set_title('Relationship: Old Method = -(New Method)')
    ax3.set_xlabel('Time (s)')
    ax3.set_ylabel('Error (m)')
    ax3.grid(True)
    ax3.legend()
    
    # Verify they are negatives of each other
    diff = altitude_error_old + down_error_new
    max_diff = np.max(np.abs(diff))
    ax3.text(0.02, 0.98, f'Max difference: {max_diff:.2e}m\n(Should be ~0)',
             transform=ax3.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    # Plot 4: 2D error for comparison
    ax4 = axes[1, 1]
    ax4.plot(data['time'], error_2d, 'b-', linewidth=2, label='2D Position Error')
    ax4.plot(data['time'], np.abs(down_error_new), 'g-', linewidth=2, label='|Vertical Position Error|')
    ax4.set_title('2D vs Vertical Position Error Magnitude')
    ax4.set_xlabel('Time (s)')
    ax4.set_ylabel('Position Error (m)')
    ax4.grid(True)
    ax4.legend()
    
    # Add statistics
    rms_2d = np.sqrt(np.mean(error_2d**2))
    rms_vert = np.sqrt(np.mean(down_error_new**2))
    ax4.text(0.02, 0.98, f'2D RMS: {rms_2d:.2f}m\nVert RMS: {rms_vert:.2f}m',
             transform=ax4.transAxes, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # Save plot
    output_file = Path("test_outputs/altitude_error_fix_comparison.png")
    output_file.parent.mkdir(exist_ok=True)
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"Comparison plot saved to: {output_file}")
    
    plt.show()
    
    return {
        'altitude_error_old': altitude_error_old,
        'down_error_new': down_error_new,
        'error_2d': error_2d,
        'rms_old': rms_old,
        'rms_new': rms_new,
        'rms_2d': rms_2d
    }

def main():
    """Main test function"""
    
    logger.info("Testing altitude error calculation fix...")
    
    # Create test data
    logger.info("Creating synthetic test data...")
    data = create_test_data()
    
    # Create comparison plots
    logger.info("Creating comparison plots...")
    results = plot_comparison(data)
    
    # Print summary
    logger.info("\n" + "="*60)
    logger.info("ALTITUDE ERROR CALCULATION ANALYSIS")
    logger.info("="*60)
    logger.info(f"Old method RMS error: {results['rms_old']:.3f} m")
    logger.info(f"New method RMS error: {results['rms_new']:.3f} m")
    logger.info(f"2D positioning RMS error: {results['rms_2d']:.3f} m")
    logger.info("\nKey findings:")
    logger.info("• Old method: altitude_error = (-est_down) - (-base_down) = -(est_down - base_down)")
    logger.info("• New method: down_error = est_down - base_down")
    logger.info("• The old method is the negative of the new method")
    logger.info("• The new method is consistent with 2D positioning error calculation")
    logger.info("• Positive down error = estimated trajectory below baseline")
    logger.info("• Negative down error = estimated trajectory above baseline")
    logger.info("="*60)

if __name__ == "__main__":
    main()
