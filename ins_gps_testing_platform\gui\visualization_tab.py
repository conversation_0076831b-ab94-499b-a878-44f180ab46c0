"""Visualization Tab - Results Display and Analysis"""
import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
import logging

logger = logging.getLogger(__name__)

class VisualizationTab:
    """Visualization tab for results display"""
    
    def __init__(self, parent, analysis_engine, main_gui):
        self.parent = parent
        self.analysis_engine = analysis_engine
        self.main_gui = main_gui
        
        # Create main frame
        self.frame = ttk.Frame(parent)
        
        # Initialize plot variables
        self.analysis_results = None
        
        # Create UI
        self.create_plot_controls()
        self.create_plot_notebook()
        
        logger.info("Visualization tab initialized")
    
    def create_plot_controls(self):
        """Create plot control buttons"""
        control_frame = ttk.Frame(self.frame)
        control_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.But<PERSON>(control_frame, text="Refresh Plots", 
                  command=self.refresh_plots).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Export Plots", 
                  command=self.export_plots).pack(side=tk.LEFT, padx=5)
        ttk.Button(control_frame, text="Clear All", 
                  command=self.clear_results).pack(side=tk.LEFT, padx=5)
        
        # Statistics label
        self.stats_label = ttk.Label(control_frame, text="No results available")
        self.stats_label.pack(side=tk.RIGHT, padx=5)
    
    def create_plot_notebook(self):
        """Create notebook for different plot categories"""
        self.plot_notebook = ttk.Notebook(self.frame)
        self.plot_notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create plot tabs
        self.create_trajectory_plots_tab()
        self.create_orientation_tab()
        self.create_error_analysis_tab()
        self.create_3d_visualization_tab()
        self.create_statistics_tab()
    
    def create_trajectory_plots_tab(self):
        """Create trajectory plots tab"""
        traj_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(traj_frame, text="Trajectory Plots")
        
        # Create matplotlib figure for 2D trajectory
        self.traj_fig, self.traj_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.traj_fig.suptitle("Trajectory Analysis")
        
        # 2D trajectory plot
        self.traj_axes[0, 0].set_title("2D Trajectory Comparison")
        self.traj_axes[0, 0].set_xlabel("East (m)")
        self.traj_axes[0, 0].set_ylabel("North (m)")
        self.traj_axes[0, 0].grid(True)
        self.traj_axes[0, 0].axis('equal')
        
        # Position vs time
        self.traj_axes[0, 1].set_title("Position vs Time")
        self.traj_axes[0, 1].set_xlabel("Time (s)")
        self.traj_axes[0, 1].set_ylabel("Position (m)")
        self.traj_axes[0, 1].grid(True)
        
        # Speed vs time
        self.traj_axes[1, 0].set_title("Speed vs Time")
        self.traj_axes[1, 0].set_xlabel("Time (s)")
        self.traj_axes[1, 0].set_ylabel("Speed (m/s)")
        self.traj_axes[1, 0].grid(True)
        
        # Altitude vs time
        self.traj_axes[1, 1].set_title("Altitude vs Time")
        self.traj_axes[1, 1].set_xlabel("Time (s)")
        self.traj_axes[1, 1].set_ylabel("Altitude (m)")
        self.traj_axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(traj_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.traj_canvas = FigureCanvasTkAgg(self.traj_fig, traj_frame)
        self.traj_canvas.draw()
        self.traj_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.traj_toolbar = NavigationToolbar2Tk(self.traj_canvas, toolbar_frame)
        self.traj_toolbar.update()

    def create_orientation_tab(self):
        """Create orientation analysis tab"""
        orient_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(orient_frame, text="Orientations")

        # Create matplotlib figure for orientation plots
        self.orient_fig, self.orient_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.orient_fig.suptitle("Orientation Analysis")

        # Ground truth orientation vs time
        self.orient_axes[0, 0].set_title("Ground Truth Orientation vs Time")
        self.orient_axes[0, 0].set_xlabel("Time (s)")
        self.orient_axes[0, 0].set_ylabel("Orientation (deg)")
        self.orient_axes[0, 0].grid(True)

        # Estimated orientation vs time
        self.orient_axes[0, 1].set_title("Estimated Orientation vs Time")
        self.orient_axes[0, 1].set_xlabel("Time (s)")
        self.orient_axes[0, 1].set_ylabel("Orientation (deg)")
        self.orient_axes[0, 1].grid(True)

        # Orientation comparison
        self.orient_axes[1, 0].set_title("Orientation Comparison")
        self.orient_axes[1, 0].set_xlabel("Time (s)")
        self.orient_axes[1, 0].set_ylabel("Orientation (deg)")
        self.orient_axes[1, 0].grid(True)

        # Orientation error vs time (moved from Error Analysis)
        self.orient_axes[1, 1].set_title("Orientation Error vs Time")
        self.orient_axes[1, 1].set_xlabel("Time (s)")
        self.orient_axes[1, 1].set_ylabel("Orientation Error (deg)")
        self.orient_axes[1, 1].grid(True)

        plt.tight_layout()

        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(orient_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.orient_canvas = FigureCanvasTkAgg(self.orient_fig, orient_frame)
        self.orient_canvas.draw()
        self.orient_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.orient_toolbar = NavigationToolbar2Tk(self.orient_canvas, toolbar_frame)
        self.orient_toolbar.update()

    def create_error_analysis_tab(self):
        """Create error analysis tab"""
        error_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(error_frame, text="Error Analysis")
        
        # Create matplotlib figure - now 2x2 with position and velocity errors only
        self.error_fig, self.error_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.error_fig.suptitle("Error Analysis")

        # Position error vs time
        self.error_axes[0, 0].set_title("Position Error vs Time")
        self.error_axes[0, 0].set_xlabel("Time (s)")
        self.error_axes[0, 0].set_ylabel("Position Error (m)")
        self.error_axes[0, 0].grid(True)

        # Error distribution
        self.error_axes[0, 1].set_title("Position Error Distribution")
        self.error_axes[0, 1].set_xlabel("Position Error (m)")
        self.error_axes[0, 1].set_ylabel("Frequency")
        self.error_axes[0, 1].grid(True)

        # Velocity error vs time
        self.error_axes[1, 0].set_title("Velocity Error vs Time")
        self.error_axes[1, 0].set_xlabel("Time (s)")
        self.error_axes[1, 0].set_ylabel("Velocity Error (m/s)")
        self.error_axes[1, 0].grid(True)

        # Additional error metrics
        self.error_axes[1, 1].set_title("Error Statistics")
        self.error_axes[1, 1].set_xlabel("Error Type")
        self.error_axes[1, 1].set_ylabel("Error Magnitude")
        self.error_axes[1, 1].grid(True)
        
        plt.tight_layout()
        
        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(error_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.error_canvas = FigureCanvasTkAgg(self.error_fig, error_frame)
        self.error_canvas.draw()
        self.error_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.error_toolbar = NavigationToolbar2Tk(self.error_canvas, toolbar_frame)
        self.error_toolbar.update()
    
    def create_3d_visualization_tab(self):
        """Create 3D visualization tab"""
        viz3d_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(viz3d_frame, text="3D Visualization")
        
        # Create matplotlib 3D figure - single plot only
        self.viz3d_fig = plt.figure(figsize=(10, 8))

        # 3D trajectory plot - full figure
        self.ax3d_traj = self.viz3d_fig.add_subplot(111, projection='3d')
        self.ax3d_traj.set_title("3D Trajectory Comparison")
        self.ax3d_traj.set_xlabel("East (m)")
        self.ax3d_traj.set_ylabel("North (m)")
        self.ax3d_traj.set_zlabel("Up (m)")
        
        plt.tight_layout()
        
        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(viz3d_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.viz3d_canvas = FigureCanvasTkAgg(self.viz3d_fig, viz3d_frame)
        self.viz3d_canvas.draw()
        self.viz3d_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.viz3d_toolbar = NavigationToolbar2Tk(self.viz3d_canvas, toolbar_frame)
        self.viz3d_toolbar.update()
    
    def create_statistics_tab(self):
        """Create statistics display tab"""
        stats_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(stats_frame, text="Statistics")
        
        # Create text widget for statistics
        text_frame = ttk.Frame(stats_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Statistics text with scrollbar
        self.stats_text = tk.Text(text_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def update_results(self, analysis_results):
        """Update visualization with new results"""
        self.analysis_results = analysis_results
        
        if analysis_results is None:
            return
        
        try:
            self.update_trajectory_plots()
            self.update_orientation_plots()
            self.update_error_plots()
            self.update_3d_plots()
            self.update_statistics()
            self.update_stats_summary()

            logger.info("Visualization updated successfully")
        except Exception as e:
            logger.error(f"Failed to update visualization: {e}")
    
    def update_trajectory_plots(self):
        """Update trajectory plots"""
        if self.analysis_results is None:
            return
        
        # Clear previous plots
        for ax in self.traj_axes.flat:
            ax.clear()
        
        # Get data
        ground_truth = self.analysis_results.ground_truth
        estimates = self.analysis_results.estimates
        gps_measurements = self.analysis_results.gps_measurements
        
        time_vec = ground_truth['time']
        true_pos = ground_truth['position']  # [N, 3] array
        est_pos = estimates['position']      # [N, 3] array
        
        # 2D trajectory plot
        ax = self.traj_axes[0, 0]
        ax.plot(true_pos[:, 1], true_pos[:, 0], 'b-', label='True Trajectory', linewidth=2)
        ax.plot(est_pos[:, 1], est_pos[:, 0], 'r--', label='Estimated', linewidth=2)
        
        if gps_measurements is not None:
            gps_pos = gps_measurements['position']
            ax.scatter(gps_pos[:, 1], gps_pos[:, 0], c='g', s=20, alpha=0.6, label='GPS Points')
        
        # Mark start and end
        ax.plot(true_pos[0, 1], true_pos[0, 0], 'go', markersize=8, label='Start')
        ax.plot(true_pos[-1, 1], true_pos[-1, 0], 'ro', markersize=8, label='End')
        
        ax.set_title("2D Trajectory Comparison")
        ax.set_xlabel("East (m)")
        ax.set_ylabel("North (m)")
        ax.grid(True)
        ax.legend()
        ax.axis('equal')
        
        # Position vs time
        ax = self.traj_axes[0, 1]
        ax.plot(time_vec, true_pos[:, 0], 'b-', label='True North', linewidth=2)
        ax.plot(time_vec, est_pos[:, 0], 'r--', label='Est North', linewidth=2)
        ax.plot(time_vec, true_pos[:, 1], 'g-', label='True East', linewidth=2)
        ax.plot(time_vec, est_pos[:, 1], 'm--', label='Est East', linewidth=2)
        ax.set_title("Position vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Position (m)")
        ax.grid(True)
        ax.legend()
        
        # Speed vs time
        ax = self.traj_axes[1, 0]
        true_vel = ground_truth['velocity']
        est_vel = estimates['velocity']
        true_speed = np.linalg.norm(true_vel, axis=1)
        est_speed = np.linalg.norm(est_vel, axis=1)
        
        ax.plot(time_vec, true_speed, 'b-', label='True Speed', linewidth=2)
        ax.plot(time_vec, est_speed, 'r--', label='Estimated Speed', linewidth=2)
        ax.set_title("Speed vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Speed (m/s)")
        ax.grid(True)
        ax.legend()
        
        # Altitude vs time
        ax = self.traj_axes[1, 1]
        ax.plot(time_vec, -true_pos[:, 2], 'b-', label='True Altitude', linewidth=2)
        ax.plot(time_vec, -est_pos[:, 2], 'r--', label='Estimated Altitude', linewidth=2)
        ax.set_title("Altitude vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Altitude (m)")
        ax.grid(True)
        ax.legend()
        
        self.traj_fig.suptitle("Trajectory Analysis")
        plt.tight_layout()
        self.traj_canvas.draw()

    def update_orientation_plots(self):
        """Update orientation analysis plots"""
        if self.analysis_results is None:
            return

        # Clear previous plots
        for ax in self.orient_axes.flat:
            ax.clear()

        # Get data
        ground_truth = self.analysis_results.ground_truth
        estimates = self.analysis_results.estimates
        errors = self.analysis_results.errors
        time_vec = ground_truth['time']

        # Extract orientation data (assuming quaternions or Euler angles)
        true_orient = ground_truth.get('orientation', None)
        est_orient = estimates.get('orientation', None)

        if true_orient is not None:
            # Convert quaternions to Euler angles if needed
            if true_orient.shape[1] == 4:  # Quaternions
                true_euler = self.quaternion_to_euler(true_orient)
            else:
                true_euler = true_orient

            # Ground truth orientation plot
            ax = self.orient_axes[0, 0]
            ax.plot(time_vec, np.degrees(true_euler[:, 0]), 'b-', label='Roll', linewidth=2)
            ax.plot(time_vec, np.degrees(true_euler[:, 1]), 'g-', label='Pitch', linewidth=2)
            ax.plot(time_vec, np.degrees(true_euler[:, 2]), 'r-', label='Yaw', linewidth=2)
            ax.set_title("Ground Truth Orientation vs Time")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Orientation (deg)")
            ax.grid(True)
            ax.legend()

        if est_orient is not None:
            # Convert quaternions to Euler angles if needed
            if est_orient.shape[1] == 4:  # Quaternions
                est_euler = self.quaternion_to_euler(est_orient)
            else:
                est_euler = est_orient

            # Estimated orientation plot
            ax = self.orient_axes[0, 1]
            ax.plot(time_vec, np.degrees(est_euler[:, 0]), 'b--', label='Roll', linewidth=2)
            ax.plot(time_vec, np.degrees(est_euler[:, 1]), 'g--', label='Pitch', linewidth=2)
            ax.plot(time_vec, np.degrees(est_euler[:, 2]), 'r--', label='Yaw', linewidth=2)
            ax.set_title("Estimated Orientation vs Time")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Orientation (deg)")
            ax.grid(True)
            ax.legend()

            # Orientation comparison plot
            if true_orient is not None:
                ax = self.orient_axes[1, 0]
                ax.plot(time_vec, np.degrees(true_euler[:, 2]), 'b-', label='True Yaw', linewidth=2)
                ax.plot(time_vec, np.degrees(est_euler[:, 2]), 'r--', label='Est Yaw', linewidth=2)
                ax.set_title("Yaw Orientation Comparison")
                ax.set_xlabel("Time (s)")
                ax.set_ylabel("Yaw Angle (deg)")
                ax.grid(True)
                ax.legend()

        # Orientation error plot (moved from Error Analysis)
        ax = self.orient_axes[1, 1]
        if 'orientation' in errors:
            orient_error = np.degrees(errors['orientation'])
            if orient_error.ndim > 1:
                # Plot individual components if available
                ax.plot(time_vec, orient_error[:, 0], 'b-', label='Roll Error', linewidth=2)
                ax.plot(time_vec, orient_error[:, 1], 'g-', label='Pitch Error', linewidth=2)
                ax.plot(time_vec, orient_error[:, 2], 'r-', label='Yaw Error', linewidth=2)
                ax.legend()
            else:
                # Plot magnitude if only scalar error available
                ax.plot(time_vec, orient_error, 'm-', linewidth=2)
        ax.set_title("Orientation Error vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Orientation Error (deg)")
        ax.grid(True)

        self.orient_fig.suptitle("Orientation Analysis")
        plt.tight_layout()
        self.orient_canvas.draw()

    def quaternion_to_euler(self, quaternions):
        """Convert quaternions to Euler angles (roll, pitch, yaw)"""
        # Assuming quaternions are in [w, x, y, z] format
        w, x, y, z = quaternions[:, 0], quaternions[:, 1], quaternions[:, 2], quaternions[:, 3]

        # Roll (x-axis rotation)
        sinr_cosp = 2 * (w * x + y * z)
        cosr_cosp = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(sinr_cosp, cosr_cosp)

        # Pitch (y-axis rotation)
        sinp = 2 * (w * y - z * x)
        pitch = np.where(np.abs(sinp) >= 1, np.copysign(np.pi / 2, sinp), np.arcsin(sinp))

        # Yaw (z-axis rotation)
        siny_cosp = 2 * (w * z + x * y)
        cosy_cosp = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(siny_cosp, cosy_cosp)

        return np.column_stack([roll, pitch, yaw])

    def update_error_plots(self):
        """Update error analysis plots"""
        if self.analysis_results is None:
            return
        
        # Clear previous plots
        for ax in self.error_axes.flat:
            ax.clear()
        
        # Get error data
        errors = self.analysis_results.errors
        time_vec = self.analysis_results.ground_truth['time']
        
        # Position error vs time
        ax = self.error_axes[0, 0]
        pos_error_2d = np.linalg.norm(errors['position'][:, :2], axis=1)
        ax.plot(time_vec, pos_error_2d, 'r-', linewidth=2)
        ax.set_title("Position Error vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("2D Position Error (m)")
        ax.grid(True)
        
        # Error distribution
        ax = self.error_axes[0, 1]
        ax.hist(pos_error_2d, bins=50, alpha=0.7, density=True)
        ax.set_title("Position Error Distribution")
        ax.set_xlabel("Position Error (m)")
        ax.set_ylabel("Density")
        ax.grid(True)
        
        # Velocity error vs time
        ax = self.error_axes[1, 0]
        vel_error = np.linalg.norm(errors['velocity'], axis=1)
        ax.plot(time_vec, vel_error, 'g-', linewidth=2)
        ax.set_title("Velocity Error vs Time")
        ax.set_xlabel("Time (s)")
        ax.set_ylabel("Velocity Error (m/s)")
        ax.grid(True)

        # Error statistics bar chart
        ax = self.error_axes[1, 1]
        stats = self.analysis_results.statistics
        error_types = ['Position\n(RMS)', 'Position\n(Max)', 'Velocity\n(RMS)', 'Velocity\n(Max)']
        error_values = [
            stats.get('rms_position_error_2d', 0),
            stats.get('max_position_error_2d', 0),
            stats.get('rms_velocity_error', 0),
            stats.get('max_velocity_error', 0)
        ]

        bars = ax.bar(error_types, error_values, color=['blue', 'lightblue', 'green', 'lightgreen'])
        ax.set_title("Error Statistics Summary")
        ax.set_ylabel("Error Magnitude")
        ax.grid(True, axis='y')

        # Add value labels on bars
        for bar, value in zip(bars, error_values):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                   f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        self.error_fig.suptitle("Error Analysis")
        plt.tight_layout()
        self.error_canvas.draw()
    
    def update_3d_plots(self):
        """Update 3D visualization plots"""
        if self.analysis_results is None:
            return
        
        # Clear previous plots
        self.ax3d_traj.clear()

        # Get data
        ground_truth = self.analysis_results.ground_truth
        estimates = self.analysis_results.estimates
        gps_measurements = self.analysis_results.gps_measurements

        true_pos = ground_truth['position']
        est_pos = estimates['position']

        # 3D trajectory plot
        self.ax3d_traj.plot(true_pos[:, 1], true_pos[:, 0], -true_pos[:, 2],
                           'b-', label='True Trajectory', linewidth=2)
        self.ax3d_traj.plot(est_pos[:, 1], est_pos[:, 0], -est_pos[:, 2],
                           'r--', label='Estimated', linewidth=2)

        # Add GPS measurements if available
        if gps_measurements is not None:
            gps_pos = gps_measurements['position']
            self.ax3d_traj.scatter(gps_pos[:, 1], gps_pos[:, 0], -gps_pos[:, 2],
                                  c='g', s=20, alpha=0.6, label='GPS Points')

        # Mark start and end points
        self.ax3d_traj.scatter([true_pos[0, 1]], [true_pos[0, 0]], [-true_pos[0, 2]],
                              c='g', s=100, label='Start')
        self.ax3d_traj.scatter([true_pos[-1, 1]], [true_pos[-1, 0]], [-true_pos[-1, 2]],
                              c='r', s=100, label='End')

        self.ax3d_traj.set_title("3D Trajectory Comparison")
        self.ax3d_traj.set_xlabel("East (m)")
        self.ax3d_traj.set_ylabel("North (m)")
        self.ax3d_traj.set_zlabel("Up (m)")
        self.ax3d_traj.legend()

        self.viz3d_canvas.draw()
    
    def update_statistics(self):
        """Update statistics display"""
        if self.analysis_results is None:
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(tk.END, "No simulation results available.")
            return
        
        # Clear existing text
        self.stats_text.delete(1.0, tk.END)
        
        # Calculate statistics
        stats = self.analysis_results.statistics
        
        stats_text = """INS/GPS Fusion Analysis Results
========================================

POSITION ERRORS:
----------------------------------------
RMS 2D Position Error:     {:.3f} m
Max 2D Position Error:     {:.3f} m
Mean 2D Position Error:    {:.3f} m
95% Position Error:        {:.3f} m

VELOCITY ERRORS:
----------------------------------------
RMS Velocity Error:        {:.3f} m/s
Max Velocity Error:        {:.3f} m/s
Mean Velocity Error:       {:.3f} m/s

ALTITUDE ERRORS:
----------------------------------------
RMS Altitude Error:        {:.3f} m
Max Altitude Error:        {:.3f} m
Mean Altitude Error:       {:.3f} m

TRAJECTORY STATISTICS:
----------------------------------------
Total Distance:            {:.1f} m
Average Speed:             {:.2f} m/s
Maximum Speed:             {:.2f} m/s
Simulation Duration:       {:.1f} s

SENSOR PARTICIPATION:
----------------------------------------
Accelerometer:             {}
Gyroscope:                 {}
Magnetometer:              {}
GPS Position:              {}
GPS Velocity:              {}

""".format(
            stats.get('rms_position_error_2d', 0),
            stats.get('max_position_error_2d', 0),
            stats.get('mean_position_error_2d', 0),
            stats.get('position_error_95th', 0),
            stats.get('rms_velocity_error', 0),
            stats.get('max_velocity_error', 0),
            stats.get('mean_velocity_error', 0),
            stats.get('rms_altitude_error', 0),
            stats.get('max_altitude_error', 0),
            stats.get('mean_altitude_error', 0),
            stats.get('total_distance', 0),
            stats.get('average_speed', 0),
            stats.get('maximum_speed', 0),
            stats.get('duration', 0),
            "Enabled" if stats.get('used_accelerometer', False) else "Disabled",
            "Enabled" if stats.get('used_gyroscope', False) else "Disabled",
            "Enabled" if stats.get('used_magnetometer', False) else "Disabled",
            "Enabled" if stats.get('used_gps_position', False) else "Disabled",
            "Enabled" if stats.get('used_gps_velocity', False) else "Disabled"
        )
        
        self.stats_text.insert(tk.END, stats_text)
    
    def update_stats_summary(self):
        """Update summary statistics in control bar"""
        if self.analysis_results is None:
            self.stats_label.config(text="No results available")
            return
        
        stats = self.analysis_results.statistics
        rms_pos = stats.get('rms_position_error_2d', 0)
        max_pos = stats.get('max_position_error_2d', 0)
        
        summary = f"RMS Error: {rms_pos:.2f}m | Max Error: {max_pos:.2f}m"
        self.stats_label.config(text=summary)
    
    def refresh_plots(self):
        """Refresh all plots"""
        if self.analysis_results is not None:
            self.update_results(self.analysis_results)
    
    def export_plots(self):
        """Export all plots"""
        if self.analysis_results is None:
            tk.messagebox.showwarning("No Data", "No plots to export.")
            return

        try:
            # Create export directory outside generator folder
            from pathlib import Path
            export_dir = Path("../exports")
            export_dir.mkdir(exist_ok=True)

            # Export trajectory plots
            self.traj_fig.savefig(export_dir / "trajectory_plots.png", dpi=300, bbox_inches='tight')

            # Export orientation plots
            self.orient_fig.savefig(export_dir / "orientation_analysis.png", dpi=300, bbox_inches='tight')

            # Export error plots
            self.error_fig.savefig(export_dir / "error_analysis.png", dpi=300, bbox_inches='tight')

            # Export 3D plots
            self.viz3d_fig.savefig(export_dir / "3d_visualization.png", dpi=300, bbox_inches='tight')

            tk.messagebox.showinfo("Export Complete", f"Plots exported to {export_dir.resolve()} directory")
        except Exception as e:
            tk.messagebox.showerror("Export Error", f"Failed to export plots:\n{str(e)}")
    
    def clear_results(self):
        """Clear all results and plots"""
        self.analysis_results = None
        
        # Clear all axes
        for ax in self.traj_axes.flat:
            ax.clear()
            ax.grid(True)

        for ax in self.orient_axes.flat:
            ax.clear()
            ax.grid(True)

        for ax in self.error_axes.flat:
            ax.clear()
            ax.grid(True)

        self.ax3d_traj.clear()

        # Redraw canvases
        self.traj_canvas.draw()
        self.orient_canvas.draw()
        self.error_canvas.draw()
        self.viz3d_canvas.draw()
        
        # Clear statistics
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.insert(tk.END, "No simulation results available.")
        
        self.stats_label.config(text="No results available")

    def show_ground_truth_only(self, ground_truth):
        """Show only ground truth trajectory (for simulation-only mode)"""
        try:
            # Clear previous plots
            for ax in self.traj_axes.flat:
                ax.clear()

            # Get ground truth data
            time_vec = ground_truth.time
            true_pos = ground_truth.position  # [N, 3] array
            true_vel = ground_truth.velocity

            # 2D trajectory plot
            ax = self.traj_axes[0, 0]
            ax.plot(true_pos[:, 1], true_pos[:, 0], 'b-', label='Ground Truth', linewidth=2)
            ax.scatter([true_pos[0, 1]], [true_pos[0, 0]], c='g', s=100, label='Start', zorder=5)
            ax.scatter([true_pos[-1, 1]], [true_pos[-1, 0]], c='r', s=100, label='End', zorder=5)
            ax.set_title("Ground Truth Trajectory (2D)")
            ax.set_xlabel("East (m)")
            ax.set_ylabel("North (m)")
            ax.grid(True)
            ax.axis('equal')
            ax.legend()

            # Velocity profile
            ax = self.traj_axes[0, 1]
            velocity_magnitude = np.linalg.norm(true_vel[:, :2], axis=1)  # 2D velocity
            ax.plot(time_vec, velocity_magnitude, 'b-', linewidth=2)
            ax.set_title("Velocity Profile")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Speed (m/s)")
            ax.grid(True)

            # Altitude profile
            ax = self.traj_axes[1, 0]
            ax.plot(time_vec, -true_pos[:, 2], 'b-', linewidth=2)  # Negative for up direction
            ax.set_title("Altitude Profile")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Altitude (m)")
            ax.grid(True)

            # Trajectory curvature
            ax = self.traj_axes[1, 1]
            # Simple curvature calculation
            if len(true_pos) > 2:
                from utils.helpers import compute_curvature
                curvature = compute_curvature(true_pos)
                ax.plot(time_vec, curvature, 'b-', linewidth=2)
            ax.set_title("Trajectory Curvature")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Curvature (1/m)")
            ax.grid(True)

            # Update 3D plot
            self.ax3d_traj.clear()
            self.ax3d_traj.plot(true_pos[:, 1], true_pos[:, 0], -true_pos[:, 2],
                               'b-', label='Ground Truth', linewidth=2)
            self.ax3d_traj.scatter([true_pos[0, 1]], [true_pos[0, 0]], [-true_pos[0, 2]],
                                  c='g', s=100, label='Start')
            self.ax3d_traj.scatter([true_pos[-1, 1]], [true_pos[-1, 0]], [-true_pos[-1, 2]],
                                  c='r', s=100, label='End')
            self.ax3d_traj.set_title("Ground Truth Trajectory (3D)")
            self.ax3d_traj.set_xlabel("East (m)")
            self.ax3d_traj.set_ylabel("North (m)")
            self.ax3d_traj.set_zlabel("Up (m)")
            self.ax3d_traj.legend()

            # Clear orientation plots (show message that no estimation data is available)
            for ax in self.orient_axes.flat:
                ax.clear()
                ax.text(0.5, 0.5, 'No Estimation Data Available\nRun estimation to see orientation analysis',
                       ha='center', va='center', transform=ax.transAxes, fontsize=12)
                ax.set_title("Orientation Analysis - No Data")

            # Redraw canvases
            self.traj_canvas.draw()
            self.orient_canvas.draw()
            self.viz3d_canvas.draw()

            # Update statistics with ground truth info
            self.stats_text.delete(1.0, tk.END)
            total_distance = np.sum(np.linalg.norm(np.diff(true_pos[:, :2], axis=0), axis=1))
            duration = time_vec[-1] - time_vec[0]
            avg_speed = total_distance / duration if duration > 0 else 0
            max_speed = np.max(velocity_magnitude)

            stats_text = f"""Ground Truth Trajectory Information
========================================

Trajectory Statistics:
• Total Distance: {total_distance:.2f} m
• Duration: {duration:.1f} s
• Average Speed: {avg_speed:.2f} m/s
• Maximum Speed: {max_speed:.2f} m/s
• Data Points: {len(time_vec)}

Position Range:
• North: {np.min(true_pos[:, 0]):.1f} to {np.max(true_pos[:, 0]):.1f} m
• East: {np.min(true_pos[:, 1]):.1f} to {np.max(true_pos[:, 1]):.1f} m
• Altitude: {np.min(-true_pos[:, 2]):.1f} to {np.max(-true_pos[:, 2]):.1f} m

Note: This is ground truth data only.
Run estimation to see comparison results.
"""

            self.stats_text.insert(tk.END, stats_text)
            self.stats_label.config(text=f"Ground Truth | Distance: {total_distance:.1f}m | Duration: {duration:.1f}s")

            logger.info("Ground truth visualization updated")

        except Exception as e:
            logger.error(f"Failed to show ground truth: {e}")
            messagebox.showerror("Visualization Error", f"Failed to show ground truth:\n{str(e)}")
