#!/usr/bin/env python3
"""
Script to copy the INS/GPS Testing Platform to light_software folder
Copies only necessary files (.py, .json, .md) and excludes temporary/cache files
"""

import os
import shutil
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_destination(dest_path):
    """Clean the destination directory"""
    if dest_path.exists():
        logger.info(f"Cleaning destination directory: {dest_path}")
        try:
            # Try to remove individual files first
            for item in dest_path.rglob('*'):
                if item.is_file():
                    try:
                        item.chmod(0o777)  # Make file writable
                        item.unlink()
                    except Exception as e:
                        logger.warning(f"Could not remove file {item}: {e}")

            # Then remove directories
            for item in sorted(dest_path.rglob('*'), key=lambda x: str(x), reverse=True):
                if item.is_dir():
                    try:
                        item.rmdir()
                    except Exception as e:
                        logger.warning(f"Could not remove directory {item}: {e}")

            # Finally try to remove the root directory
            if dest_path.exists():
                shutil.rmtree(dest_path, ignore_errors=True)

        except Exception as e:
            logger.warning(f"Could not completely clean destination: {e}")
            logger.info("Continuing with copy process...")

    # Create the destination directory
    dest_path.mkdir(parents=True, exist_ok=True)
    logger.info(f"Destination directory ready: {dest_path}")

def should_copy_file(file_path, script_name):
    """Determine if a file should be copied"""
    file_name = file_path.name
    file_suffix = file_path.suffix.lower()
    
    # Skip the copy script itself
    if file_name == script_name:
        return False
    
    # Include these file types
    allowed_extensions = {'.py', '.json', '.md'}
    if file_suffix in allowed_extensions:
        return True
    
    # Skip these file types
    excluded_extensions = {'.pyc', '.pyo', '.png', '.jpg', '.jpeg', '.bin', '.log', '.tmp'}
    if file_suffix in excluded_extensions:
        return False
    
    # Skip these specific files/patterns
    excluded_patterns = {
        '__pycache__',
        '.git',
        '.gitignore',
        '.pytest_cache',
        'node_modules',
        '.vscode',
        '.idea',
        'Thumbs.db',
        '.DS_Store'
    }
    
    if file_name in excluded_patterns:
        return False
    
    # Skip hidden files (starting with .)
    if file_name.startswith('.') and file_name not in {'.gitignore'}:
        return False
    
    return False  # Default: don't copy unknown file types

def should_copy_directory(dir_path):
    """Determine if a directory should be copied"""
    dir_name = dir_path.name
    
    # Skip these directories
    excluded_dirs = {
        '__pycache__',
        '.git',
        '.pytest_cache',
        'node_modules',
        '.vscode',
        '.idea',
        'exports',  # Skip exports directory as it contains generated files
        'temp',
        'tmp'
    }
    
    if dir_name in excluded_dirs:
        return False
    
    # Skip hidden directories
    if dir_name.startswith('.') and dir_name not in {'.github'}:
        return False
    
    return True

def copy_files_recursive(src_path, dest_path, script_name):
    """Recursively copy files from source to destination"""
    copied_files = 0
    skipped_files = 0
    
    for item in src_path.iterdir():
        if item.is_file():
            if should_copy_file(item, script_name):
                # Create destination file path
                dest_file = dest_path / item.name

                try:
                    # Copy the file
                    shutil.copy2(item, dest_file)
                    logger.info(f"Copied: {item.relative_to(src_path.parent)}")
                    copied_files += 1
                except Exception as e:
                    logger.warning(f"Failed to copy {item.relative_to(src_path.parent)}: {e}")
                    skipped_files += 1
            else:
                logger.debug(f"Skipped file: {item.relative_to(src_path.parent)}")
                skipped_files += 1
                
        elif item.is_dir():
            if should_copy_directory(item):
                # Create destination directory
                dest_dir = dest_path / item.name
                dest_dir.mkdir(exist_ok=True)
                
                # Recursively copy contents
                sub_copied, sub_skipped = copy_files_recursive(item, dest_dir, script_name)
                copied_files += sub_copied
                skipped_files += sub_skipped
                
                # Remove empty directories
                if not any(dest_dir.iterdir()):
                    dest_dir.rmdir()
                    logger.debug(f"Removed empty directory: {dest_dir.relative_to(dest_path.parent)}")
            else:
                logger.debug(f"Skipped directory: {item.relative_to(src_path.parent)}")
                skipped_files += 1
    
    return copied_files, skipped_files

def main():
    """Main function to copy the application"""
    # Define paths
    source_path = Path(r"F:\One_Drive\OneDrive - Université Laval\!Projects\Mitacs\EnvisioningLabs\codes\python\mytoolbox\generator\ins_gps_testing_platform")
    dest_path = Path(r"F:\One_Drive\OneDrive - Université Laval\!Projects\Mitacs\EnvisioningLabs\codes\python\mytoolbox\light_software\ins_gps_testing_platform")
    
    script_name = Path(__file__).name
    
    logger.info("="*60)
    logger.info("INS/GPS Testing Platform - Copy to Light Software")
    logger.info("="*60)
    logger.info(f"Source: {source_path}")
    logger.info(f"Destination: {dest_path}")
    logger.info(f"Script: {script_name} (will be excluded)")
    logger.info("="*60)
    
    # Verify source exists
    if not source_path.exists():
        logger.error(f"Source directory does not exist: {source_path}")
        return 1
    
    try:
        # Clean destination
        clean_destination(dest_path)
        
        # Copy files
        logger.info("Starting file copy process...")
        copied_files, skipped_files = copy_files_recursive(source_path, dest_path, script_name)
        
        # Summary
        logger.info("="*60)
        logger.info("Copy process completed successfully!")
        logger.info(f"Files copied: {copied_files}")
        logger.info(f"Files skipped: {skipped_files}")
        logger.info(f"Destination: {dest_path}")
        logger.info("="*60)
        
        # List some key files that should be present
        key_files = [
            "main.py",
            "gui/main_gui.py",
            "pohang/pohang_integration.py",
            "preprocessing/marine_imu_preprocessor.py",
            "README.md"
        ]
        
        logger.info("Verifying key files:")
        for key_file in key_files:
            file_path = dest_path / key_file
            if file_path.exists():
                logger.info(f"✓ {key_file}")
            else:
                logger.warning(f"✗ {key_file} (not found)")
        
        return 0
        
    except Exception as e:
        logger.error(f"Error during copy process: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
