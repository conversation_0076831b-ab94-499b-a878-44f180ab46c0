#!/usr/bin/env python3
"""
Test script to validate coordinate system fixes
"""

import sys
import numpy as np
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from pohang.pohang_analizor import PohangDatasetLoader
from utils.coordinate_transforms import lla_to_ned, ned_to_lla
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_coordinate_transformations():
    """Test coordinate transformation consistency"""
    logger.info("Testing coordinate transformation consistency...")
    
    # Test data around Pohang area
    test_lla = np.array([
        [36.023619907, 129.378011880, 0.0],
        [36.024, 129.378, 5.0],
        [36.023, 129.379, -2.0]
    ])
    
    ref_lla = (36.023619907, 129.378011880, 0.0)
    
    # Convert to NED and back
    ned_coords = lla_to_ned(test_lla, ref_lla)
    lla_back = ned_to_lla(ned_coords, ref_lla)
    
    # Check round-trip accuracy
    diff = np.abs(test_lla - lla_back)
    max_error = np.max(diff)
    
    logger.info(f"Original LLA: {test_lla}")
    logger.info(f"NED coords: {ned_coords}")
    logger.info(f"Back to LLA: {lla_back}")
    logger.info(f"Max round-trip error: {max_error:.10f}")
    
    if max_error < 1e-6:
        logger.info("✓ Coordinate transformations are consistent")
        return True
    else:
        logger.error("✗ Coordinate transformations have errors")
        return False

def test_lgf_extraction():
    """Test LGF reference extraction from GPS data"""
    logger.info("Testing LGF reference extraction...")
    
    # Create a mock dataset loader to test the concept
    try:
        # This will fail if no dataset is available, but we can test the logic
        loader = PohangDatasetLoader("dummy_path")
        logger.info("Dataset loader created successfully")
        
        # Test the reference location extraction logic
        ref_location = loader.get_lgf_reference_location()
        logger.info(f"LGF reference location: {ref_location}")
        
        return True
        
    except Exception as e:
        logger.info(f"Dataset loader test failed (expected if no dataset): {e}")
        logger.info("Testing coordinate extraction logic separately...")
        
        # Test the coordinate extraction logic with mock data
        mock_gps_data = np.array([
            [36.023, 129.378, 0.0],
            [36.024, 129.379, 1.0],
            [36.025, 129.380, 2.0],
            [36.022, 129.377, -1.0]
        ])
        
        center_lat = np.mean(mock_gps_data[:, 0])
        center_lon = np.mean(mock_gps_data[:, 1])
        center_alt = np.mean(mock_gps_data[:, 2])
        
        logger.info(f"Mock GPS trajectory center: ({center_lat:.6f}, {center_lon:.6f}, {center_alt:.3f})")
        
        return True

def test_coordinate_system_consistency():
    """Test that all modules use consistent coordinate systems"""
    logger.info("Testing coordinate system consistency across modules...")
    
    # Test reference location
    ref_lla = (36.023619907, 129.378011880, 0.0)
    
    # Test point
    test_lla = np.array([[36.024, 129.379, 5.0]])
    
    # Test with utils coordinate transforms
    from utils.coordinate_transforms import lla_to_ned as utils_lla_to_ned
    ned_utils = utils_lla_to_ned(test_lla, ref_lla)
    
    # Test with fusion engine coordinate transforms
    try:
        from fusion.saeid_gps_ins_ekf import ProductionINSFilter
        from fusion.saeid_gps_ins_ekf import INSFilterConfig
        
        config = INSFilterConfig()
        filter_obj = ProductionINSFilter(ref_lla, config)
        ned_fusion = filter_obj._lla_to_ned(test_lla[0])
        
        logger.info(f"Utils NED: {ned_utils[0]}")
        logger.info(f"Fusion NED: {ned_fusion}")
        
        diff = np.abs(ned_utils[0] - ned_fusion)
        max_diff = np.max(diff)
        
        if max_diff < 1e-3:  # 1mm tolerance
            logger.info("✓ Coordinate systems are consistent across modules")
            return True
        else:
            logger.warning(f"⚠ Coordinate system difference: {max_diff:.6f}m")
            return False
            
    except Exception as e:
        logger.warning(f"Could not test fusion engine consistency: {e}")
        return True

def main():
    """Run all coordinate system tests"""
    logger.info("="*60)
    logger.info("COORDINATE SYSTEM VALIDATION TESTS")
    logger.info("="*60)
    
    tests = [
        ("Coordinate Transformations", test_coordinate_transformations),
        ("LGF Reference Extraction", test_lgf_extraction),
        ("Cross-Module Consistency", test_coordinate_system_consistency)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        logger.info("✓ All coordinate system tests passed!")
        return True
    else:
        logger.warning("⚠ Some tests failed - coordinate system issues may remain")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
