#!/usr/bin/env python3
"""
Test script to verify local coordinate conversion for Pohang results visualization
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_local_coordinate_conversion():
    """Test the local coordinate conversion with actual Pohang data"""
    
    # Load the trajectory data
    results_dir = Path("../exports/pohang_results")
    trajectory_file = results_dir / "trajectory_data.csv"
    
    if not trajectory_file.exists():
        logger.error(f"Trajectory data file not found: {trajectory_file}")
        return False
        
    # Load data
    trajectory_data = pd.read_csv(trajectory_file)
    logger.info(f"Loaded trajectory data with {len(trajectory_data)} points")
    
    # Extract coordinates
    est_north = trajectory_data['est_north'].values
    est_east = trajectory_data['est_east'].values
    base_north = trajectory_data['base_north'].values
    base_east = trajectory_data['base_east'].values
    
    # Calculate reference point (baseline centroid), handling NaN values
    valid_mask = ~(np.isnan(base_north) | np.isnan(base_east))
    if np.any(valid_mask):
        base_north_valid = base_north[valid_mask]
        base_east_valid = base_east[valid_mask]
        reference_point = np.array([np.mean(base_north_valid), np.mean(base_east_valid)])
        logger.info(f"Reference point (baseline centroid): N={reference_point[0]:.1f}, E={reference_point[1]:.1f}")
        logger.info(f"Baseline data: {len(base_north_valid)} valid points out of {len(base_north)} total")
    else:
        logger.error("No valid baseline data found!")
        return False
    
    # Convert to local coordinates
    est_local_north = est_north - reference_point[0]
    est_local_east = est_east - reference_point[1]
    base_local_north = base_north - reference_point[0]
    base_local_east = base_east - reference_point[1]
    
    # Print coordinate ranges
    logger.info("\nOriginal coordinate ranges:")
    logger.info(f"  Estimated: N=[{est_north.min():.1f}, {est_north.max():.1f}], E=[{est_east.min():.1f}, {est_east.max():.1f}]")
    logger.info(f"  Baseline:  N=[{base_north_valid.min():.1f}, {base_north_valid.max():.1f}], E=[{base_east_valid.min():.1f}, {base_east_valid.max():.1f}]")

    logger.info("\nLocal coordinate ranges:")
    logger.info(f"  Estimated: N=[{est_local_north.min():.1f}, {est_local_north.max():.1f}], E=[{est_local_east.min():.1f}, {est_local_east.max():.1f}]")

    # For baseline local coordinates, only show valid points
    base_local_north_valid = base_local_north[valid_mask]
    base_local_east_valid = base_local_east[valid_mask]
    logger.info(f"  Baseline:  N=[{base_local_north_valid.min():.1f}, {base_local_north_valid.max():.1f}], E=[{base_local_east_valid.min():.1f}, {base_local_east_valid.max():.1f}]")
    
    # Create comparison plots
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Original coordinates
    ax1.plot(est_east, est_north, 'b-', linewidth=2, label='Estimated', alpha=0.7)
    ax1.plot(base_east_valid, base_north_valid, 'g--', linewidth=2, label='Baseline', alpha=0.7)
    ax1.set_title('Original Coordinates (UTM)')
    ax1.set_xlabel('East (m)')
    ax1.set_ylabel('North (m)')
    ax1.grid(True)
    ax1.legend()
    ax1.axis('equal')

    # Local coordinates
    ax2.plot(est_local_east, est_local_north, 'b-', linewidth=2, label='Estimated', alpha=0.7)
    ax2.plot(base_local_east_valid, base_local_north_valid, 'g--', linewidth=2, label='Baseline', alpha=0.7)
    ax2.set_title('Local Coordinates (Centered)')
    ax2.set_xlabel('Local East (m)')
    ax2.set_ylabel('Local North (m)')
    ax2.grid(True)
    ax2.legend()
    ax2.axis('equal')
    
    # Add reference point marker
    ax2.plot(0, 0, 'ro', markersize=8, label='Local Origin')
    ax2.legend()
    
    plt.tight_layout()
    
    # Save the plot
    output_file = results_dir / "coordinate_conversion_test.png"
    plt.savefig(output_file, dpi=150, bbox_inches='tight')
    logger.info(f"Comparison plot saved to: {output_file}")
    
    # Calculate improvement in coordinate range
    original_range_north = est_north.max() - est_north.min()
    original_range_east = est_east.max() - est_east.min()
    local_range_north = est_local_north.max() - est_local_north.min()
    local_range_east = est_local_east.max() - est_local_east.min()
    
    logger.info(f"\nCoordinate range comparison:")
    logger.info(f"  Original ranges: N={original_range_north:.1f}m, E={original_range_east:.1f}m")
    logger.info(f"  Local ranges:    N={local_range_north:.1f}m, E={local_range_east:.1f}m")
    logger.info(f"  Range preserved: {abs(original_range_north - local_range_north) < 0.1}")
    
    # Calculate positioning errors in both coordinate systems
    error_original = np.sqrt((est_north - base_north)**2 + (est_east - base_east)**2)
    error_local = np.sqrt((est_local_north - base_local_north)**2 + (est_local_east - base_local_east)**2)
    
    logger.info(f"\nPositioning error comparison:")
    logger.info(f"  Original coords RMS error: {np.sqrt(np.mean(error_original**2)):.3f}m")
    logger.info(f"  Local coords RMS error:    {np.sqrt(np.mean(error_local**2)):.3f}m")
    logger.info(f"  Error preserved: {abs(np.sqrt(np.mean(error_original**2)) - np.sqrt(np.mean(error_local**2))) < 0.001}")
    
    plt.show()
    return True

if __name__ == "__main__":
    logger.info("Testing local coordinate conversion for Pohang results...")
    success = test_local_coordinate_conversion()
    if success:
        logger.info("✓ Local coordinate conversion test completed successfully")
    else:
        logger.error("✗ Local coordinate conversion test failed")
