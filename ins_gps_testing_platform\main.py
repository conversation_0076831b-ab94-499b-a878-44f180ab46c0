#!/usr/bin/env python3
"""
INS/GPS Fusion Testing Platform - Main Entry Point
Production-ready testing platform for boat motion compensation evaluation
"""

import sys
import os
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui.main_gui import MainGUI
import tkinter as tk
from tkinter import messagebox
import logging

def setup_logging():
    """Setup application logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('insP_gps_platform.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """Main application entry point"""
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Starting INS/GPS Testing Platform...")
        
        # Create main GUI
        root = tk.Tk()
        app = MainGUI(root)
        
        # Start GUI event loop
        root.mainloop()
        
    except Exception as e:
        messagebox.showerror("Application Error", f"Failed to start application:\n{str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
