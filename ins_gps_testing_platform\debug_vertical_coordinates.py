#!/usr/bin/env python3
"""
Debug script to examine actual vertical coordinate values in Pohang data
to understand why vertical position errors are so large.
"""

import pandas as pd
import numpy as np
from pathlib import Path
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def analyze_trajectory_data():
    """Analyze the actual trajectory data to understand coordinate values"""
    
    # Check if trajectory data exists
    trajectory_file = Path("../exports/pohang_results/trajectory_data.csv")
    
    if not trajectory_file.exists():
        logger.error(f"Trajectory data file not found: {trajectory_file}")
        logger.info("Please run Pohang processing first to generate the data")
        return None
    
    # Load trajectory data
    logger.info(f"Loading trajectory data from: {trajectory_file}")
    df = pd.read_csv(trajectory_file)
    
    logger.info(f"Loaded {len(df)} data points")
    logger.info(f"Columns: {list(df.columns)}")
    
    # Analyze coordinate values
    if 'est_down' in df.columns and 'base_down' in df.columns:
        
        est_down = df['est_down'].values
        base_down = df['base_down'].values
        
        logger.info("\n" + "="*60)
        logger.info("VERTICAL COORDINATE ANALYSIS")
        logger.info("="*60)
        
        # Estimated trajectory vertical coordinates
        logger.info(f"\nEstimated Down Coordinates:")
        logger.info(f"  Min: {np.min(est_down):.2f}m")
        logger.info(f"  Max: {np.max(est_down):.2f}m")
        logger.info(f"  Mean: {np.mean(est_down):.2f}m")
        logger.info(f"  Range: {np.max(est_down) - np.min(est_down):.2f}m")
        logger.info(f"  Std: {np.std(est_down):.2f}m")
        
        # Baseline trajectory vertical coordinates  
        logger.info(f"\nBaseline Down Coordinates:")
        logger.info(f"  Min: {np.min(base_down):.2f}m")
        logger.info(f"  Max: {np.max(base_down):.2f}m")
        logger.info(f"  Mean: {np.mean(base_down):.2f}m")
        logger.info(f"  Range: {np.max(base_down) - np.min(base_down):.2f}m")
        logger.info(f"  Std: {np.std(base_down):.2f}m")
        
        # Calculate differences
        down_diff = est_down - base_down
        
        logger.info(f"\nVertical Position Error (est_down - base_down):")
        logger.info(f"  Min: {np.min(down_diff):.2f}m")
        logger.info(f"  Max: {np.max(down_diff):.2f}m")
        logger.info(f"  Mean: {np.mean(down_diff):.2f}m")
        logger.info(f"  RMS: {np.sqrt(np.mean(down_diff**2)):.2f}m")
        logger.info(f"  Std: {np.std(down_diff):.2f}m")
        
        # Check for systematic offset
        mean_offset = np.mean(down_diff)
        logger.info(f"\nSystematic Offset Analysis:")
        logger.info(f"  Mean offset: {mean_offset:.2f}m")
        if abs(mean_offset) > 1000:
            logger.warning("  ⚠️  LARGE SYSTEMATIC OFFSET DETECTED!")
            logger.warning("  This suggests coordinate system reference mismatch")
        
        # Analyze coordinate system characteristics
        logger.info(f"\nCoordinate System Analysis:")
        
        # Check if estimated coordinates look like GPS-derived (could be large)
        if abs(np.mean(est_down)) > 100:
            logger.warning(f"  ⚠️  Estimated down coordinates are large (mean: {np.mean(est_down):.1f}m)")
            logger.warning("  This suggests GPS altitude reference issue")
        
        # Check if baseline coordinates look like local depth (should be small)
        if abs(np.mean(base_down)) < 50:
            logger.info(f"  ✓ Baseline down coordinates look like local depth (mean: {np.mean(base_down):.1f}m)")
        else:
            logger.warning(f"  ⚠️  Baseline down coordinates are unexpectedly large (mean: {np.mean(base_down):.1f}m)")
        
        return {
            'est_down': est_down,
            'base_down': base_down,
            'down_error': down_diff,
            'systematic_offset': mean_offset
        }
    
    else:
        logger.error("Required columns 'est_down' and 'base_down' not found in data")
        return None

def analyze_error_data():
    """Analyze the error data from trajectory comparison"""
    
    error_file = Path("../exports/pohang_results/error_metrics.csv")
    
    if not error_file.exists():
        logger.warning(f"Error data file not found: {error_file}")
        return None
    
    logger.info(f"Loading error data from: {error_file}")
    df = pd.read_csv(error_file)
    
    if 'position_error_down' in df.columns:
        down_error = df['position_error_down'].values
        
        logger.info(f"\nTrajectory Comparison Down Error:")
        logger.info(f"  Min: {np.min(down_error):.2f}m")
        logger.info(f"  Max: {np.max(down_error):.2f}m")
        logger.info(f"  Mean: {np.mean(down_error):.2f}m")
        logger.info(f"  RMS: {np.sqrt(np.mean(down_error**2)):.2f}m")
        logger.info(f"  Std: {np.std(down_error):.2f}m")
        
        return down_error
    
    return None

def compare_calculation_methods():
    """Compare different error calculation methods"""
    
    trajectory_data = analyze_trajectory_data()
    error_data = analyze_error_data()
    
    if trajectory_data is not None and error_data is not None:
        
        # Compare trajectory data calculation vs trajectory comparison calculation
        traj_error = trajectory_data['down_error']
        comp_error = error_data
        
        # Ensure same length for comparison
        min_len = min(len(traj_error), len(comp_error))
        traj_error = traj_error[:min_len]
        comp_error = comp_error[:min_len]
        
        logger.info(f"\n" + "="*60)
        logger.info("ERROR CALCULATION METHOD COMPARISON")
        logger.info("="*60)
        
        logger.info(f"\nTrajectory Data Method (est_down - base_down):")
        logger.info(f"  RMS: {np.sqrt(np.mean(traj_error**2)):.2f}m")
        logger.info(f"  Mean: {np.mean(traj_error):.2f}m")
        
        logger.info(f"\nTrajectory Comparison Method (SVD aligned):")
        logger.info(f"  RMS: {np.sqrt(np.mean(comp_error**2)):.2f}m")
        logger.info(f"  Mean: {np.mean(comp_error):.2f}m")
        
        # Check correlation
        correlation = np.corrcoef(traj_error, comp_error)[0, 1]
        logger.info(f"\nCorrelation between methods: {correlation:.3f}")
        
        if correlation < 0.5:
            logger.warning("  ⚠️  Low correlation suggests different coordinate systems!")
        
        # Check if one method has systematic offset
        diff = traj_error - comp_error
        mean_diff = np.mean(diff)
        logger.info(f"Mean difference (traj - comp): {mean_diff:.2f}m")
        
        if abs(mean_diff) > 1000:
            logger.warning("  ⚠️  Large systematic difference between methods!")

def main():
    """Main analysis function"""
    
    logger.info("Analyzing vertical coordinate values in Pohang data...")
    logger.info("This will help understand why vertical position errors are so large")
    
    # Analyze trajectory data
    trajectory_data = analyze_trajectory_data()
    
    # Analyze error data  
    error_data = analyze_error_data()
    
    # Compare methods
    compare_calculation_methods()
    
    logger.info("\n" + "="*60)
    logger.info("SUMMARY")
    logger.info("="*60)
    logger.info("This analysis should reveal:")
    logger.info("1. Whether estimated/baseline coordinates have different reference systems")
    logger.info("2. If there's a large systematic offset in vertical coordinates")
    logger.info("3. How trajectory data vs trajectory comparison methods differ")
    logger.info("="*60)

if __name__ == "__main__":
    main()
