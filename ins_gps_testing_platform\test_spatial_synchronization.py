#!/usr/bin/env python3
"""
Test script to verify spatial-based trajectory synchronization implementation.

This script creates synthetic trajectories with time offset and tests whether
spatial synchronization produces more accurate results than time-based synchronization.
"""

import numpy as np
import matplotlib.pyplot as plt
import logging
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from pohang.trajectory_comparison import TrajectoryComparator

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_synthetic_trajectories():
    """Create synthetic trajectories with known time offset to test spatial synchronization"""
    
    # Create a circular trajectory
    t = np.linspace(0, 10, 100)  # 10 seconds, 100 points
    radius = 50.0
    
    # True trajectory (baseline)
    true_x = radius * np.cos(0.5 * t)  # North
    true_y = radius * np.sin(0.5 * t)  # East
    true_z = -5 + 2 * np.sin(0.2 * t)  # Down (varying depth)
    true_pos = np.column_stack([true_x, true_y, true_z])
    
    # Estimated trajectory with time offset and some noise
    time_offset = 0.5  # 0.5 second time offset
    t_est = t + time_offset  # Estimated trajectory is offset in time
    
    # Add some positioning error
    noise_level = 2.0  # 2 meter standard deviation
    est_x = radius * np.cos(0.5 * t_est) + np.random.normal(0, noise_level, len(t))
    est_y = radius * np.sin(0.5 * t_est) + np.random.normal(0, noise_level, len(t))
    est_z = -5 + 2 * np.sin(0.2 * t_est) + np.random.normal(0, 0.5, len(t))
    est_pos = np.column_stack([est_x, est_y, est_z])
    
    # Create trajectory dictionaries
    baseline_trajectory = {
        'time': t,
        'position': true_pos,
        'quaternion': np.tile([1, 0, 0, 0], (len(t), 1))  # Identity quaternions
    }
    
    estimated_trajectory = {
        'time': t,  # Same time grid for estimated (this is the key issue)
        'position': est_pos,
        'orientation': np.tile([1, 0, 0, 0], (len(t), 1))  # Identity quaternions
    }
    
    logger.info(f"Created synthetic trajectories:")
    logger.info(f"  Baseline: {len(true_pos)} points")
    logger.info(f"  Estimated: {len(est_pos)} points")
    logger.info(f"  Time offset: {time_offset} seconds")
    logger.info(f"  Noise level: {noise_level} meters")
    
    return estimated_trajectory, baseline_trajectory, time_offset, noise_level

def test_spatial_vs_time_synchronization():
    """Test spatial synchronization vs time-based synchronization"""
    
    logger.info("="*60)
    logger.info("TESTING SPATIAL VS TIME-BASED SYNCHRONIZATION")
    logger.info("="*60)
    
    # Create synthetic data
    estimated_traj, baseline_traj, time_offset, noise_level = create_synthetic_trajectories()
    
    # Test with spatial synchronization (new method)
    logger.info("\n1. Testing with SPATIAL synchronization:")
    comparator_spatial = TrajectoryComparator()
    metrics_spatial = comparator_spatial.compute_errors(estimated_traj, baseline_traj)
    
    logger.info(f"   Spatial sync results:")
    logger.info(f"     3D RMSE: {metrics_spatial['position']['3d_rmse']:.3f} m")
    logger.info(f"     Matched points: {metrics_spatial['num_matched_points']}")
    if hasattr(comparator_spatial, 'spatial_sync_indices') and comparator_spatial.spatial_sync_indices:
        distances = comparator_spatial.spatial_sync_indices['distances']
        logger.info(f"     Mean matching distance: {np.mean(distances):.3f} m")
        logger.info(f"     Max matching distance: {np.max(distances):.3f} m")
    
    # Create a simple time-based comparison for reference
    logger.info("\n2. Testing with TIME-BASED synchronization (simple):")
    
    # Simple time-based error calculation (what we're trying to improve)
    min_len = min(len(estimated_traj['position']), len(baseline_traj['position']))
    est_pos_time = estimated_traj['position'][:min_len]
    base_pos_time = baseline_traj['position'][:min_len]
    
    # Calculate errors using time-based matching
    time_errors = est_pos_time - base_pos_time
    time_3d_errors = np.linalg.norm(time_errors, axis=1)
    time_rmse = np.sqrt(np.mean(time_3d_errors**2))
    
    logger.info(f"   Time-based results:")
    logger.info(f"     3D RMSE: {time_rmse:.3f} m")
    logger.info(f"     Matched points: {min_len}")
    
    # Compare results
    logger.info("\n3. COMPARISON:")
    improvement = ((time_rmse - metrics_spatial['position']['3d_rmse']) / time_rmse) * 100
    logger.info(f"   Time-based RMSE: {time_rmse:.3f} m")
    logger.info(f"   Spatial-based RMSE: {metrics_spatial['position']['3d_rmse']:.3f} m")
    logger.info(f"   Improvement: {improvement:.1f}%")
    
    if improvement > 0:
        logger.info("   ✓ Spatial synchronization is MORE accurate!")
    else:
        logger.info("   ⚠ Spatial synchronization is LESS accurate")
    
    # Expected result analysis
    logger.info(f"\n4. EXPECTED vs ACTUAL:")
    logger.info(f"   Expected noise level: {noise_level:.1f} m")
    logger.info(f"   Time offset effect: {time_offset:.1f} s")
    logger.info(f"   Spatial sync should be closer to noise level")
    
    return metrics_spatial, time_rmse, improvement

def create_visualization(estimated_traj, baseline_traj, comparator):
    """Create visualization of the spatial synchronization results"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Spatial Synchronization Test Results', fontsize=16)
    
    # Plot 1: Original trajectories
    ax = axes[0, 0]
    ax.plot(estimated_traj['position'][:, 1], estimated_traj['position'][:, 0], 
            'b-', label='Estimated', linewidth=2, alpha=0.8)
    ax.plot(baseline_traj['position'][:, 1], baseline_traj['position'][:, 0], 
            'r-', label='Baseline (True)', linewidth=2, alpha=0.8)
    ax.set_xlabel('East (m)')
    ax.set_ylabel('North (m)')
    ax.set_title('Original Trajectories')
    ax.legend()
    ax.grid(True)
    ax.axis('equal')
    
    # Plot 2: Aligned trajectories
    ax = axes[0, 1]
    ax.plot(estimated_traj['position'][:, 1], estimated_traj['position'][:, 0], 
            'b-', label='Estimated', linewidth=2, alpha=0.8)
    if hasattr(comparator, 'aligned_baseline_pos') and comparator.aligned_baseline_pos is not None:
        ax.plot(comparator.aligned_baseline_pos[:, 1], comparator.aligned_baseline_pos[:, 0], 
                'g--', label='Baseline (Aligned)', linewidth=2, alpha=0.8)
    ax.set_xlabel('East (m)')
    ax.set_ylabel('North (m)')
    ax.set_title('After Spatial Alignment')
    ax.legend()
    ax.grid(True)
    ax.axis('equal')
    
    # Plot 3: Error time series
    ax = axes[1, 0]
    if 'time_series' in comparator.metrics:
        time = comparator.metrics['time_series']['time']
        errors_3d = comparator.metrics['time_series']['position_3d_errors']
        ax.plot(time, errors_3d, 'k-', linewidth=2)
        ax.set_xlabel('Time (s)')
        ax.set_ylabel('3D Position Error (m)')
        ax.set_title('Position Error vs Time')
        ax.grid(True)
        
        # Add RMSE line
        rmse = comparator.metrics['position']['3d_rmse']
        ax.axhline(y=rmse, color='r', linestyle='--', 
                  label=f'RMSE: {rmse:.3f}m')
        ax.legend()
    
    # Plot 4: Error statistics
    ax = axes[1, 1]
    ax.axis('off')
    
    # Create statistics text
    stats_text = "Spatial Synchronization Results:\n\n"
    stats_text += f"3D RMSE: {comparator.metrics['position']['3d_rmse']:.3f} m\n"
    stats_text += f"3D MAE: {comparator.metrics['position']['3d_mae']:.3f} m\n"
    stats_text += f"3D Max: {comparator.metrics['position']['3d_max']:.3f} m\n"
    stats_text += f"Matched points: {comparator.metrics['num_matched_points']}\n\n"
    
    if hasattr(comparator, 'spatial_sync_indices') and comparator.spatial_sync_indices:
        distances = comparator.spatial_sync_indices['distances']
        stats_text += f"Spatial Matching:\n"
        stats_text += f"  Mean distance: {np.mean(distances):.3f} m\n"
        stats_text += f"  Max distance: {np.max(distances):.3f} m\n"
    
    ax.text(0.05, 0.95, stats_text, transform=ax.transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    
    # Save the plot
    output_dir = Path("../exports")
    output_dir.mkdir(exist_ok=True)
    output_file = output_dir / "spatial_synchronization_test.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"📊 Test visualization saved to: {output_file}")
    
    plt.close()

def main():
    """Main test function"""
    try:
        # Run the test
        estimated_traj, baseline_traj, time_offset, noise_level = create_synthetic_trajectories()
        metrics_spatial, time_rmse, improvement = test_spatial_vs_time_synchronization()
        
        # Create visualization
        comparator = TrajectoryComparator()
        comparator.compute_errors(estimated_traj, baseline_traj)
        create_visualization(estimated_traj, baseline_traj, comparator)
        
        # Final summary
        logger.info("\n" + "="*60)
        logger.info("TEST SUMMARY")
        logger.info("="*60)
        logger.info(f"✓ Spatial synchronization implementation tested")
        logger.info(f"✓ Improvement over time-based: {improvement:.1f}%")
        logger.info(f"✓ Visualization saved to ../exports/spatial_synchronization_test.png")
        
        if improvement > 10:
            logger.info("🎯 EXCELLENT: Spatial synchronization shows significant improvement!")
        elif improvement > 0:
            logger.info("✓ GOOD: Spatial synchronization shows improvement")
        else:
            logger.info("⚠ WARNING: Spatial synchronization may need tuning")
        
        return True
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
