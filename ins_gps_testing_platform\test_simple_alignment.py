#!/usr/bin/env python3
"""
Test simple coordinate system alignment between estimated and baseline trajectories
"""

import sys
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import os

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_simple_alignment():
    """Test the simple coordinate alignment approach"""
    
    logger.info("Testing simple coordinate system alignment...")
    
    # Create mock trajectories in different coordinate systems
    
    # Baseline trajectory (ground truth) - centered around origin
    t = np.linspace(0, 100, 200)
    baseline_x = 50 * np.sin(0.1 * t)  # North
    baseline_y = 50 * np.cos(0.1 * t)  # East  
    baseline_z = -5 + 2 * np.sin(0.05 * t)  # Down
    baseline_pos = np.column_stack([baseline_x, baseline_y, baseline_z])
    
    # Estimated trajectory (with offset and some error) - different coordinate system
    offset = np.array([100, -50, 3])  # Arbitrary offset
    est_x = 50 * np.sin(0.1 * t) + np.random.normal(0, 2, len(t)) + offset[0]
    est_y = 50 * np.cos(0.1 * t) + np.random.normal(0, 2, len(t)) + offset[1]
    est_z = -5 + 2 * np.sin(0.05 * t) + np.random.normal(0, 0.5, len(t)) + offset[2]
    est_pos_original = np.column_stack([est_x, est_y, est_z])
    
    logger.info("Original trajectories:")
    logger.info(f"  Baseline center: [{np.mean(baseline_pos, axis=0)}]")
    logger.info(f"  Estimated center: [{np.mean(est_pos_original, axis=0)}]")
    logger.info(f"  Offset: [{np.mean(est_pos_original, axis=0) - np.mean(baseline_pos, axis=0)}]")
    
    # Apply simple alignment: translate estimated to baseline coordinate system
    est_center = np.mean(est_pos_original, axis=0)
    baseline_center = np.mean(baseline_pos, axis=0)
    est_pos_aligned = est_pos_original - est_center + baseline_center
    
    logger.info("\nAfter alignment:")
    logger.info(f"  Baseline center: [{np.mean(baseline_pos, axis=0)}]")
    logger.info(f"  Estimated center: [{np.mean(est_pos_aligned, axis=0)}]")
    logger.info(f"  Remaining offset: [{np.mean(est_pos_aligned, axis=0) - np.mean(baseline_pos, axis=0)}]")
    
    # Calculate errors before and after alignment
    errors_before = est_pos_original - baseline_pos
    errors_after = est_pos_aligned - baseline_pos
    
    rms_before = np.sqrt(np.mean(np.sum(errors_before**2, axis=1)))
    rms_after = np.sqrt(np.mean(np.sum(errors_after**2, axis=1)))
    
    logger.info(f"\nRMS Error:")
    logger.info(f"  Before alignment: {rms_before:.3f} m")
    logger.info(f"  After alignment: {rms_after:.3f} m")
    
    # Create visualization
    create_alignment_visualization(baseline_pos, est_pos_original, est_pos_aligned)
    
    # Verify alignment worked
    center_diff = np.linalg.norm(np.mean(est_pos_aligned, axis=0) - np.mean(baseline_pos, axis=0))
    if center_diff < 1e-10:
        logger.info("✓ Coordinate alignment successful!")
        return True
    else:
        logger.error(f"✗ Alignment failed - center difference: {center_diff:.6f}")
        return False

def create_alignment_visualization(baseline_pos, est_pos_original, est_pos_aligned):
    """Create visualization showing before/after alignment"""
    
    fig = plt.figure(figsize=(18, 12))
    fig.suptitle('Simple Coordinate System Alignment Test', fontsize=16)
    
    # Plot 1: Before alignment (3D)
    ax1 = fig.add_subplot(231, projection='3d')
    ax1.plot(baseline_pos[:, 1], baseline_pos[:, 0], -baseline_pos[:, 2], 'r-', linewidth=2, label='Baseline (GT)', alpha=0.8)
    ax1.plot(est_pos_original[:, 1], est_pos_original[:, 0], -est_pos_original[:, 2], 'b-', linewidth=2, label='Estimated (Original)', alpha=0.8)
    ax1.set_xlabel('East (m)')
    ax1.set_ylabel('North (m)')
    ax1.set_zlabel('Up (m)')
    ax1.set_title('Before Alignment (3D)')
    ax1.legend()
    
    # Plot 2: After alignment (3D)
    ax2 = fig.add_subplot(232, projection='3d')
    ax2.plot(baseline_pos[:, 1], baseline_pos[:, 0], -baseline_pos[:, 2], 'r-', linewidth=2, label='Baseline (GT)', alpha=0.8)
    ax2.plot(est_pos_aligned[:, 1], est_pos_aligned[:, 0], -est_pos_aligned[:, 2], 'b-', linewidth=2, label='Estimated (Aligned)', alpha=0.8)
    ax2.set_xlabel('East (m)')
    ax2.set_ylabel('North (m)')
    ax2.set_zlabel('Up (m)')
    ax2.set_title('After Alignment (3D)')
    ax2.legend()
    
    # Plot 3: Before alignment (2D top view)
    ax3 = fig.add_subplot(233)
    ax3.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r-', linewidth=2, label='Baseline (GT)', alpha=0.8)
    ax3.plot(est_pos_original[:, 1], est_pos_original[:, 0], 'b-', linewidth=2, label='Estimated (Original)', alpha=0.8)
    ax3.set_xlabel('East (m)')
    ax3.set_ylabel('North (m)')
    ax3.set_title('Before Alignment (Top View)')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.axis('equal')
    
    # Plot 4: After alignment (2D top view)
    ax4 = fig.add_subplot(234)
    ax4.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r-', linewidth=2, label='Baseline (GT)', alpha=0.8)
    ax4.plot(est_pos_aligned[:, 1], est_pos_aligned[:, 0], 'b-', linewidth=2, label='Estimated (Aligned)', alpha=0.8)
    ax4.set_xlabel('East (m)')
    ax4.set_ylabel('North (m)')
    ax4.set_title('After Alignment (Top View)')
    ax4.grid(True, alpha=0.3)
    ax4.legend()
    ax4.axis('equal')
    
    # Plot 5: Error analysis
    ax5 = fig.add_subplot(235)
    errors_before = est_pos_original - baseline_pos
    errors_after = est_pos_aligned - baseline_pos
    
    error_mag_before = np.sqrt(np.sum(errors_before**2, axis=1))
    error_mag_after = np.sqrt(np.sum(errors_after**2, axis=1))
    
    time = np.arange(len(baseline_pos))
    ax5.plot(time, error_mag_before, 'r-', linewidth=2, label='Before Alignment', alpha=0.8)
    ax5.plot(time, error_mag_after, 'g-', linewidth=2, label='After Alignment', alpha=0.8)
    ax5.set_xlabel('Sample')
    ax5.set_ylabel('3D Error (m)')
    ax5.set_title('Error Magnitude Comparison')
    ax5.grid(True, alpha=0.3)
    ax5.legend()
    
    # Plot 6: Statistics
    ax6 = fig.add_subplot(236)
    ax6.axis('off')
    
    rms_before = np.sqrt(np.mean(error_mag_before**2))
    rms_after = np.sqrt(np.mean(error_mag_after**2))
    
    stats_text = "Alignment Results:\n\n"
    stats_text += f"RMS Error Before: {rms_before:.3f} m\n"
    stats_text += f"RMS Error After: {rms_after:.3f} m\n"
    stats_text += f"Improvement: {rms_before - rms_after:.3f} m\n\n"
    
    baseline_center = np.mean(baseline_pos, axis=0)
    est_center_orig = np.mean(est_pos_original, axis=0)
    est_center_aligned = np.mean(est_pos_aligned, axis=0)
    
    stats_text += f"Centers:\n"
    stats_text += f"Baseline: [{baseline_center[0]:.1f}, {baseline_center[1]:.1f}, {baseline_center[2]:.1f}]\n"
    stats_text += f"Est (orig): [{est_center_orig[0]:.1f}, {est_center_orig[1]:.1f}, {est_center_orig[2]:.1f}]\n"
    stats_text += f"Est (aligned): [{est_center_aligned[0]:.1f}, {est_center_aligned[1]:.1f}, {est_center_aligned[2]:.1f}]\n\n"
    
    stats_text += "Method:\n"
    stats_text += "Simple translation:\n"
    stats_text += "aligned = original - est_center + baseline_center"
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=10,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))
    
    plt.tight_layout()
    
    os.makedirs("test_outputs", exist_ok=True)
    alignment_file = "test_outputs/simple_alignment_test.png"
    plt.savefig(alignment_file, dpi=300, bbox_inches='tight')
    logger.info(f"📊 Alignment test saved to: {os.path.abspath(alignment_file)}")
    
    plt.close()

def main():
    """Run simple alignment test"""
    logger.info("="*60)
    logger.info("SIMPLE COORDINATE ALIGNMENT TEST")
    logger.info("="*60)
    
    try:
        success = test_simple_alignment()
        
        if success:
            logger.info("\n✓ Simple alignment approach works correctly!")
            logger.info("This method can be applied to align estimated trajectory with baseline.")
        else:
            logger.error("\n✗ Simple alignment approach failed!")
        
        return success
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
