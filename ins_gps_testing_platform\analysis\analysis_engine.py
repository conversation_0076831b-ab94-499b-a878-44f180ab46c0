"""Analysis Engine - Error Analysis and Statistics"""
import numpy as np
import pandas as pd
import h5py
import scipy.io
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from simulation.simulation_engine import SimulationData
from fusion.fusion_engine import EstimationResults

logger = logging.getLogger(__name__)

@dataclass
class AnalysisResults:
    """Complete analysis results"""
    ground_truth: Dict[str, np.ndarray]
    estimates: Dict[str, np.ndarray]
    errors: Dict[str, np.ndarray]
    statistics: Dict[str, float]
    gps_measurements: Optional[Dict[str, np.ndarray]] = None

class AnalysisEngine:
    """Main analysis engine for error computation and statistics"""
    
    def __init__(self):
        pass
    
    def run_analysis(self, simulation_data: SimulationData, 
                    estimation_results: EstimationResults) -> AnalysisResults:
        """
        Run complete error analysis and statistics computation
        
        Args:
            simulation_data: Original simulation data with ground truth
            estimation_results: INS filter estimation results
            
        Returns:
            AnalysisResults containing all analysis data
        """
        logger.info("Starting analysis...")
        
        # Interpolate ground truth to estimation time grid
        ground_truth_interp = self._interpolate_ground_truth(
            simulation_data.ground_truth, estimation_results.time
        )
        
        # Compute errors
        errors = self._compute_errors(ground_truth_interp, estimation_results)
        
        # Compute statistics
        statistics = self._compute_statistics(errors, ground_truth_interp, 
                                            estimation_results, simulation_data)
        
        # Extract GPS measurements for visualization
        gps_measurements = self._extract_gps_measurements(simulation_data)
        
        # Package results
        analysis_results = AnalysisResults(
            ground_truth=ground_truth_interp,
            estimates={
                'time': estimation_results.time,
                'position': estimation_results.position,
                'velocity': estimation_results.velocity,
                'acceleration': estimation_results.acceleration,
                'orientation': estimation_results.orientation,
                'angular_velocity': estimation_results.angular_velocity
            },
            errors=errors,
            statistics=statistics,
            gps_measurements=gps_measurements
        )
        
        logger.info("Analysis completed successfully")
        return analysis_results
    
    def _interpolate_ground_truth(self, ground_truth, target_times: np.ndarray) -> Dict:
        """Interpolate ground truth to target time grid"""
        
        interpolated = {}
        
        # Interpolate position
        interpolated['position'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['position'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.position[:, i]
            )
        
        # Interpolate velocity
        interpolated['velocity'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['velocity'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.velocity[:, i]
            )
        
        # Interpolate acceleration
        interpolated['acceleration'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['acceleration'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.acceleration[:, i]
            )
        
        # Interpolate orientation (quaternions - more complex)
        interpolated['orientation'] = self._interpolate_quaternions(
            ground_truth.time, ground_truth.orientation, target_times
        )
        
        # Interpolate angular velocity
        interpolated['angular_velocity'] = np.zeros((len(target_times), 3))
        for i in range(3):
            interpolated['angular_velocity'][:, i] = np.interp(
                target_times, ground_truth.time, ground_truth.angular_velocity[:, i]
            )
        
        interpolated['time'] = target_times
        
        return interpolated
    
    def _interpolate_quaternions(self, source_times: np.ndarray, 
                               source_quats: np.ndarray, 
                               target_times: np.ndarray) -> np.ndarray:
        """Interpolate quaternions using SLERP (simplified)"""
        from scipy.spatial.transform import Rotation, Slerp
        
        # Create rotation objects
        rotations = Rotation.from_quat(source_quats[:, [1, 2, 3, 0]])  # [x,y,z,w]
        
        # Create SLERP interpolator
        slerp = Slerp(source_times, rotations)
        
        # Interpolate
        interp_rotations = slerp(target_times)
        interp_quats = interp_rotations.as_quat()  # [x,y,z,w]
        
        # Convert back to [w,x,y,z] format
        return interp_quats[:, [3, 0, 1, 2]]
    
    def _compute_errors(self, ground_truth: Dict, 
                       estimation_results: EstimationResults) -> Dict:
        """Compute all error metrics"""
        
        errors = {}
        
        # Position errors
        errors['position'] = estimation_results.position - ground_truth['position']
        
        # Velocity errors
        errors['velocity'] = estimation_results.velocity - ground_truth['velocity']
        
        # Acceleration errors
        errors['acceleration'] = estimation_results.acceleration - ground_truth['acceleration']
        
        # Orientation errors (simplified - using rotation vector difference)
        errors['orientation'] = self._compute_orientation_errors(
            ground_truth['orientation'], estimation_results.orientation
        )
        
        # Angular velocity errors
        errors['angular_velocity'] = estimation_results.angular_velocity - ground_truth['angular_velocity']
        
        return errors
    
    def _compute_orientation_errors(self, true_quats: np.ndarray, 
                                  est_quats: np.ndarray) -> np.ndarray:
        """Compute orientation errors in rotation vector form"""
        from scipy.spatial.transform import Rotation
        
        n_samples = len(true_quats)
        errors = np.zeros(n_samples)
        
        for i in range(n_samples):
            # Convert to rotation objects
            r_true = Rotation.from_quat(true_quats[i, [1, 2, 3, 0]])  # [x,y,z,w]
            r_est = Rotation.from_quat(est_quats[i, [1, 2, 3, 0]])   # [x,y,z,w]
            
            # Relative rotation
            r_error = r_est * r_true.inv()
            
            # Convert to rotation vector magnitude
            rotvec = r_error.as_rotvec()
            errors[i] = np.linalg.norm(rotvec)
        
        return errors
    
    def _compute_statistics(self, errors: Dict, ground_truth: Dict, 
                          estimation_results: EstimationResults, 
                          simulation_data: SimulationData) -> Dict:
        """Compute comprehensive statistics"""
        
        stats = {}
        
        # Position error statistics
        pos_error_2d = np.linalg.norm(errors['position'][:, :2], axis=1)
        stats['rms_position_error_2d'] = np.sqrt(np.mean(pos_error_2d**2))
        stats['max_position_error_2d'] = np.max(pos_error_2d)
        stats['mean_position_error_2d'] = np.mean(pos_error_2d)
        stats['position_error_95th'] = np.percentile(pos_error_2d, 95)
        
        # Altitude error statistics
        alt_error = np.abs(errors['position'][:, 2])
        stats['rms_altitude_error'] = np.sqrt(np.mean(alt_error**2))
        stats['max_altitude_error'] = np.max(alt_error)
        stats['mean_altitude_error'] = np.mean(alt_error)
        
        # Velocity error statistics
        vel_error = np.linalg.norm(errors['velocity'], axis=1)
        stats['rms_velocity_error'] = np.sqrt(np.mean(vel_error**2))
        stats['max_velocity_error'] = np.max(vel_error)
        stats['mean_velocity_error'] = np.mean(vel_error)
        
        # Orientation error statistics
        orient_error_deg = np.degrees(errors['orientation'])
        stats['rms_orientation_error'] = np.sqrt(np.mean(orient_error_deg**2))
        stats['max_orientation_error'] = np.max(orient_error_deg)
        stats['mean_orientation_error'] = np.mean(orient_error_deg)
        
        # Trajectory statistics
        true_pos = ground_truth['position']
        distances = np.linalg.norm(np.diff(true_pos, axis=0), axis=1)
        stats['total_distance'] = np.sum(distances)
        
        true_speeds = np.linalg.norm(ground_truth['velocity'], axis=1)
        stats['average_speed'] = np.mean(true_speeds)
        stats['maximum_speed'] = np.max(true_speeds)
        
        # Simulation parameters
        stats['duration'] = estimation_results.time[-1] - estimation_results.time[0]
        
        # Sensor participation
        sensor_config = simulation_data.configuration['sensors']
        stats['used_accelerometer'] = sensor_config.use_accelerometer
        stats['used_gyroscope'] = sensor_config.use_gyroscope
        stats['used_magnetometer'] = sensor_config.use_magnetometer
        stats['used_gps_position'] = sensor_config.use_gps_position
        stats['used_gps_velocity'] = sensor_config.use_gps_velocity
        
        return stats
    
    def _extract_gps_measurements(self, simulation_data: SimulationData) -> Dict:
        """Extract GPS measurements for visualization"""
        
        sensor_measurements = simulation_data.sensor_measurements
        
        # Convert GPS LLA to NED for visualization
        if len(sensor_measurements.gps_position) > 0:
            gps_lla = sensor_measurements.gps_position
            
            # Remove NaN values
            valid_mask = ~np.any(np.isnan(gps_lla), axis=1)
            if np.any(valid_mask):
                gps_lla_valid = gps_lla[valid_mask]
                
                # Use standardized LLA to NED conversion
                from utils.coordinate_transforms import lla_to_ned

                traj_config = simulation_data.configuration['trajectory']
                ref_lla = (traj_config.center_lat, traj_config.center_lon, traj_config.center_alt)

                gps_ned = lla_to_ned(gps_lla_valid, ref_lla)
                
                return {
                    'position': gps_ned,
                    'time': sensor_measurements.time['gps_position'][valid_mask]
                }
        
        return None
    

    
    def export_data(self, simulation_data: SimulationData, filename: str):
        """Export simulation data in various formats"""
        
        file_ext = filename.lower().split('.')[-1]
        
        if file_ext == 'csv':
            self._export_csv(simulation_data, filename)
        elif file_ext == 'mat':
            self._export_mat(simulation_data, filename)
        elif file_ext == 'h5':
            self._export_hdf5(simulation_data, filename)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        logger.info(f"Data exported to: {filename}")
    
    def _export_csv(self, simulation_data: SimulationData, filename: str):
        """Export to CSV format"""
        
        # Create comprehensive DataFrame
        ground_truth = simulation_data.ground_truth
        
        df = pd.DataFrame({
            'time': ground_truth.time,
            'pos_north': ground_truth.position[:, 0],
            'pos_east': ground_truth.position[:, 1],
            'pos_down': ground_truth.position[:, 2],
            'vel_north': ground_truth.velocity[:, 0],
            'vel_east': ground_truth.velocity[:, 1],
            'vel_down': ground_truth.velocity[:, 2],
            'acc_north': ground_truth.acceleration[:, 0],
            'acc_east': ground_truth.acceleration[:, 1],
            'acc_down': ground_truth.acceleration[:, 2],
            'quat_w': ground_truth.orientation[:, 0],
            'quat_x': ground_truth.orientation[:, 1],
            'quat_y': ground_truth.orientation[:, 2],
            'quat_z': ground_truth.orientation[:, 3],
            'omega_x': ground_truth.angular_velocity[:, 0],
            'omega_y': ground_truth.angular_velocity[:, 1],
            'omega_z': ground_truth.angular_velocity[:, 2]
        })
        
        df.to_csv(filename, index=False)
    
    def _export_mat(self, simulation_data: SimulationData, filename: str):
        """Export to MATLAB format"""
        
        ground_truth = simulation_data.ground_truth
        sensor_measurements = simulation_data.sensor_measurements
        
        mat_data = {
            'ground_truth': {
                'time': ground_truth.time,
                'position': ground_truth.position,
                'velocity': ground_truth.velocity,
                'acceleration': ground_truth.acceleration,
                'orientation': ground_truth.orientation,
                'angular_velocity': ground_truth.angular_velocity
            },
            'sensor_measurements': {
                'accelerometer': sensor_measurements.accelerometer,
                'gyroscope': sensor_measurements.gyroscope,
                'magnetometer': sensor_measurements.magnetometer,
                'gps_position': sensor_measurements.gps_position,
                'gps_velocity': sensor_measurements.gps_velocity,
                'time_vectors': sensor_measurements.time
            },
            'configuration': simulation_data.configuration
        }
        
        scipy.io.savemat(filename, mat_data)
    
    def _export_hdf5(self, simulation_data: SimulationData, filename: str):
        """Export to HDF5 format"""
        
        with h5py.File(filename, 'w') as f:
            # Ground truth group
            gt_group = f.create_group('ground_truth')
            gt_group.create_dataset('time', data=simulation_data.ground_truth.time)
            gt_group.create_dataset('position', data=simulation_data.ground_truth.position)
            gt_group.create_dataset('velocity', data=simulation_data.ground_truth.velocity)
            gt_group.create_dataset('acceleration', data=simulation_data.ground_truth.acceleration)
            gt_group.create_dataset('orientation', data=simulation_data.ground_truth.orientation)
            gt_group.create_dataset('angular_velocity', data=simulation_data.ground_truth.angular_velocity)
            
            # Sensor measurements group
            sensor_group = f.create_group('sensor_measurements')
            sensor_group.create_dataset('accelerometer', data=simulation_data.sensor_measurements.accelerometer)
            sensor_group.create_dataset('gyroscope', data=simulation_data.sensor_measurements.gyroscope)
            sensor_group.create_dataset('magnetometer', data=simulation_data.sensor_measurements.magnetometer)
            sensor_group.create_dataset('gps_position', data=simulation_data.sensor_measurements.gps_position)
            sensor_group.create_dataset('gps_velocity', data=simulation_data.sensor_measurements.gps_velocity)
            
            # Time vectors
            time_group = sensor_group.create_group('time_vectors')
            for sensor, times in simulation_data.sensor_measurements.time.items():
                time_group.create_dataset(sensor, data=times)
