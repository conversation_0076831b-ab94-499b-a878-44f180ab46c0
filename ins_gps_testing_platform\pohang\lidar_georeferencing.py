"""
LiDAR Georeferencing and Trajectory Comparison Tools for Pohang Dataset
Handles transformation of LiDAR data from sensor frame to geodetic coordinates
"""

import numpy as np
import json
import os
from pathlib import Path
from typing import Dict, Tuple, Optional, List, Union
from scipy.spatial.transform import Rotation
from dataclasses import dataclass
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for threading
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import logging

logger = logging.getLogger(__name__)

@dataclass
class LidarExtrinsics:
    """Calibration parameters for LiDAR sensor"""
    quaternion: np.ndarray  # [w, x, y, z] - rotation from body to sensor frame
    translation: np.ndarray  # [x, y, z] - translation from body to sensor frame in meters
    
    @property
    def rotation_matrix(self) -> np.ndarray:
        """Get rotation matrix from quaternion (body to sensor)"""
        # Convert quaternion to rotation matrix
        # Note: scipy uses [x, y, z, w] format
        q = self.quaternion
        r = Rotation.from_quat([q[1], q[2], q[3], q[0]])
        return r.as_matrix()
    
    @property
    def transform_matrix(self) -> np.ndarray:
        """Get 4x4 homogeneous transformation matrix (body to sensor)"""
        T = np.eye(4)
        T[:3, :3] = self.rotation_matrix
        T[:3, 3] = self.translation
        return T

class LidarGeoreferencer:
    """
    Handles georeferencing of LiDAR point clouds using INS/GPS fusion results
    
    Transformation chain:
    1. LiDAR sensor frame -> Vehicle body frame (using extrinsics)
    2. Vehicle body frame -> Navigation (NED) frame (using INS orientation)
    3. Navigation frame -> Local geodetic frame (using INS position)
    4. (Optional) Local NED -> Global geodetic (LLA) coordinates
    """
    
    def __init__(self, extrinsics_file: str, reference_location: Tuple[float, float, float]):
        """
        Initialize LiDAR georeferencer
        
        Args:
            extrinsics_file: Path to extrinsics.json file
            reference_location: (lat, lon, alt) reference for NED frame
        """
        self.extrinsics = self._load_extrinsics(extrinsics_file)
        self.ref_lat = np.radians(reference_location[0])
        self.ref_lon = np.radians(reference_location[1])
        self.ref_alt = reference_location[2]
        
        # Use standardized earth parameters for coordinate conversions
        from utils.constants import EARTH_RADIUS, EARTH_FLATTENING
        self.earth_radius = EARTH_RADIUS
        self.earth_flattening = EARTH_FLATTENING
        
        # Precompute earth radii
        self._compute_earth_radii()
        
    def _load_extrinsics(self, extrinsics_file: str) -> Dict[str, LidarExtrinsics]:
        """Load sensor extrinsics from JSON file"""
        with open(extrinsics_file, 'r') as f:
            data = json.load(f)
        
        extrinsics = {}
        for sensor_name, params in data.items():
            if 'lidar' in sensor_name.lower():
                extrinsics[sensor_name] = LidarExtrinsics(
                    quaternion=np.array(params['quaternion']),
                    translation=np.array(params['translation'])
                )
                logger.info(f"Loaded extrinsics for {sensor_name}")
        
        return extrinsics
    
    def _compute_earth_radii(self):
        """Compute earth radii for coordinate transformations"""
        a = self.earth_radius
        f = self.earth_flattening
        e2 = 2*f - f**2
        
        # Radius of curvature in meridian
        self.M = a * (1 - e2) / (1 - e2 * np.sin(self.ref_lat)**2)**(3/2)
        
        # Radius of curvature in prime vertical
        self.N = a / np.sqrt(1 - e2 * np.sin(self.ref_lat)**2)
    
    def transform_lidar_to_body(self, 
                               points: np.ndarray, 
                               sensor_name: str = 'lidar_front') -> np.ndarray:
        """
        Transform LiDAR points from sensor frame to vehicle body frame
        
        Args:
            points: Point cloud in sensor frame [N x 3] or [N x 4]
            sensor_name: Name of the LiDAR sensor (from extrinsics)
            
        Returns:
            Points in body frame [N x 3]
        """
        if sensor_name not in self.extrinsics:
            raise ValueError(f"No extrinsics found for sensor: {sensor_name}")
        
        extrinsic = self.extrinsics[sensor_name]
        
        # Handle both 3D and 4D points
        if points.shape[1] == 3:
            # Add homogeneous coordinate
            ones = np.ones((points.shape[0], 1))
            points_hom = np.hstack([points, ones])
        else:
            points_hom = points
        
        # Get transformation from sensor to body
        # Note: The extrinsics give body-to-sensor, so we need to invert
        T_body_to_sensor = extrinsic.transform_matrix
        T_sensor_to_body = np.linalg.inv(T_body_to_sensor)
        
        # Transform points
        points_body = (T_sensor_to_body @ points_hom.T).T
        
        return points_body[:, :3]
    
    def transform_body_to_ned(self,
                             points_body: np.ndarray,
                             orientation_quat: np.ndarray,
                             position_ned: np.ndarray) -> np.ndarray:
        """
        Transform points from body frame to NED frame
        
        Args:
            points_body: Points in body frame [N x 3]
            orientation_quat: Vehicle orientation quaternion [w, x, y, z] (NED to body)
            position_ned: Vehicle position in NED [north, east, down]
            
        Returns:
            Points in NED frame [N x 3]
        """
        # Create rotation from quaternion
        # The quaternion represents rotation from NED to body, so we need inverse
        q = orientation_quat
        r = Rotation.from_quat([q[1], q[2], q[3], q[0]])  # scipy format
        R_ned_to_body = r.as_matrix()
        R_body_to_ned = R_ned_to_body.T
        
        # Rotate points to NED
        points_ned = (R_body_to_ned @ points_body.T).T
        
        # Translate by vehicle position
        points_ned += position_ned
        
        return points_ned
    
    def transform_ned_to_lla(self, points_ned: np.ndarray) -> np.ndarray:
        """
        Transform points from NED to geodetic (LLA) coordinates
        
        Args:
            points_ned: Points in NED frame [N x 3] [north, east, down]
            
        Returns:
            Points in LLA [N x 3] [lat, lon, alt] in degrees and meters
        """
        north = points_ned[:, 0]
        east = points_ned[:, 1]
        down = points_ned[:, 2]
        
        # Convert NED to LLA
        dlat = north / (self.M + self.ref_alt)
        dlon = east / ((self.N + self.ref_alt) * np.cos(self.ref_lat))
        
        lat = self.ref_lat + dlat
        lon = self.ref_lon + dlon
        alt = self.ref_alt - down
        
        # Convert to degrees
        lat_deg = np.degrees(lat)
        lon_deg = np.degrees(lon)
        
        return np.column_stack([lat_deg, lon_deg, alt])
    
    def georeference_point_cloud(self,
                                lidar_points: np.ndarray,
                                ins_orientation: np.ndarray,
                                ins_position_ned: np.ndarray,
                                sensor_name: str = 'lidar_front',
                                output_frame: str = 'ned') -> np.ndarray:
        """
        Complete georeferencing pipeline for LiDAR point cloud
        
        Args:
            lidar_points: Raw LiDAR points in sensor frame [N x 3]
            ins_orientation: INS orientation quaternion [w, x, y, z]
            ins_position_ned: INS position in NED [north, east, down]
            sensor_name: Name of the LiDAR sensor
            output_frame: Output coordinate frame ('ned' or 'lla')
            
        Returns:
            Georeferenced points in specified frame
        """
        # Step 1: Sensor to body frame
        points_body = self.transform_lidar_to_body(lidar_points, sensor_name)
        
        # Step 2: Body to NED frame
        points_ned = self.transform_body_to_ned(
            points_body, ins_orientation, ins_position_ned
        )
        
        # Step 3: Optional conversion to LLA
        if output_frame == 'lla':
            return self.transform_ned_to_lla(points_ned)
        else:
            return points_ned
    
    def georeference_trajectory(self,
                               ins_positions: np.ndarray,
                               ins_orientations: np.ndarray,
                               sensor_name: str = 'lidar_front') -> np.ndarray:
        """
        Transform sensor trajectory to NED frame (useful for visualization)
        
        Args:
            ins_positions: Vehicle positions in NED [N x 3]
            ins_orientations: Vehicle orientations [N x 4] quaternions
            sensor_name: Name of the sensor
            
        Returns:
            Sensor positions in NED frame [N x 3]
        """
        if sensor_name not in self.extrinsics:
            # If no extrinsics, return vehicle positions
            return ins_positions
        
        extrinsic = self.extrinsics[sensor_name]
        sensor_positions = []
        
        for i in range(len(ins_positions)):
            # Get sensor position in body frame
            sensor_pos_body = extrinsic.translation
            
            # Transform to NED
            sensor_pos_ned = self.transform_body_to_ned(
                sensor_pos_body.reshape(1, -1),
                ins_orientations[i],
                ins_positions[i]
            )
            
            sensor_positions.append(sensor_pos_ned[0])
        
        return np.array(sensor_positions)


class TrajectoryComparator:
    """Compare fusion results with baseline trajectory"""
    
    def __init__(self):
        self.metrics = {}

    def _detect_coordinate_systems(self, estimated_trajectory: Dict, baseline_trajectory: Dict):
        """Detect and log coordinate system information"""

        est_pos = estimated_trajectory['position']
        base_pos = baseline_trajectory['position']

        est_range = np.abs(est_pos).max(axis=0)
        base_range = np.abs(base_pos).max(axis=0)

        logger.info("Coordinate System Analysis:")
        logger.info(f"Estimated trajectory range: N={est_range[0]:.1f}, E={est_range[1]:.1f}, D={est_range[2]:.1f}")
        logger.info(f"Baseline trajectory range: N={base_range[0]:.1f}, E={base_range[1]:.1f}, D={base_range[2]:.1f}")

        # Check if baseline is in UTM/absolute coordinates
        if base_range[0] > 100000 or base_range[1] > 100000:
            logger.warning("⚠️  COORDINATE SYSTEM MISMATCH DETECTED!")
            logger.warning("   Baseline: UTM/absolute coordinates (large values)")
            logger.warning("   Estimated: Local NED coordinates (small values)")
            logger.warning("   Large errors expected - coordinate conversion needed")
            return "utm_vs_local"
        else:
            logger.info("✓ Both trajectories appear to be in local coordinates")
            return "both_local"

    def compute_errors(self,
                      estimated_trajectory: Dict,
                      baseline_trajectory: Dict,
                      time_tolerance: float = 0.01) -> Dict:
        """
        Compute error metrics between estimated and baseline trajectories
        
        Args:
            estimated_trajectory: Dict with 'time', 'position', 'orientation' arrays
            baseline_trajectory: Dict with 'time', 'position', 'quaternion' arrays
            time_tolerance: Time tolerance for matching samples
            
        Returns:
            Dictionary of error metrics
        """
        # Detect coordinate system mismatch
        coord_system_type = self._detect_coordinate_systems(estimated_trajectory, baseline_trajectory)

        # Synchronize trajectories to common time points
        est_time = estimated_trajectory['time']
        base_time = baseline_trajectory['time']
        
        # Find matching time indices
        matched_indices = []
        for i, t_est in enumerate(est_time):
            # Find closest baseline time
            idx = np.argmin(np.abs(base_time - t_est))
            if np.abs(base_time[idx] - t_est) < time_tolerance:
                matched_indices.append((i, idx))
        
        if not matched_indices:
            raise ValueError("No matching time points found between trajectories")
        
        # Extract matched data
        est_idx, base_idx = zip(*matched_indices)
        est_pos = estimated_trajectory['position'][list(est_idx)]
        base_pos = baseline_trajectory['position'][list(base_idx)]
        
        # Compute position errors
        position_errors = est_pos - base_pos
        position_rmse = np.sqrt(np.mean(position_errors**2, axis=0))
        position_mae = np.mean(np.abs(position_errors), axis=0)
        position_max = np.max(np.abs(position_errors), axis=0)
        
        # Compute total 3D errors
        position_3d_errors = np.linalg.norm(position_errors, axis=1)
        position_3d_rmse = np.sqrt(np.mean(position_3d_errors**2))
        position_3d_mae = np.mean(position_3d_errors)
        position_3d_max = np.max(position_3d_errors)
        
        # Compute orientation errors if available
        orientation_errors = None
        if 'orientation' in estimated_trajectory and 'quaternion' in baseline_trajectory:
            est_quat = estimated_trajectory['orientation'][list(est_idx)]
            base_quat = baseline_trajectory['quaternion'][list(base_idx)]
            
            # Compute quaternion differences
            orientation_errors = []
            for i in range(len(est_quat)):
                # Convert to Rotation objects
                r_est = Rotation.from_quat([est_quat[i, 1], est_quat[i, 2], 
                                          est_quat[i, 3], est_quat[i, 0]])
                r_base = Rotation.from_quat([base_quat[i, 1], base_quat[i, 2], 
                                           base_quat[i, 3], base_quat[i, 0]])
                
                # Compute relative rotation
                r_error = r_est * r_base.inv()
                
                # Convert to angle
                angle_error = r_error.magnitude()  # radians
                orientation_errors.append(angle_error)
            
            orientation_errors = np.array(orientation_errors)
            orientation_rmse = np.sqrt(np.mean(orientation_errors**2))
            orientation_mae = np.mean(orientation_errors)
            orientation_max = np.max(orientation_errors)
        
        # Store results
        self.metrics = {
            'num_matched_points': len(matched_indices),
            'position': {
                'rmse': position_rmse,  # [north, east, down]
                'mae': position_mae,
                'max': position_max,
                '3d_rmse': position_3d_rmse,
                '3d_mae': position_3d_mae,
                '3d_max': position_3d_max
            },
            'time_series': {
                'time': est_time[list(est_idx)],
                'position_errors': position_errors,
                'position_3d_errors': position_3d_errors
            }
        }
        
        if orientation_errors is not None:
            self.metrics['orientation'] = {
                'rmse': np.degrees(orientation_rmse),  # degrees
                'mae': np.degrees(orientation_mae),
                'max': np.degrees(orientation_max)
            }
            self.metrics['time_series']['orientation_errors'] = np.degrees(orientation_errors)
        
        return self.metrics
    
    def plot_comparison(self, 
                       estimated_trajectory: Dict,
                       baseline_trajectory: Dict,
                       output_file: Optional[str] = None):
        """Plot trajectory comparison"""
        
        fig = plt.figure(figsize=(15, 10))
        
        # 3D trajectory plot
        ax1 = fig.add_subplot(221, projection='3d')
        ax1.plot(estimated_trajectory['position'][:, 1],  # East
                estimated_trajectory['position'][:, 0],   # North
                -estimated_trajectory['position'][:, 2],  # Up
                'b-', label='Estimated', linewidth=2)
        ax1.plot(baseline_trajectory['position'][:, 1],
                baseline_trajectory['position'][:, 0],
                -baseline_trajectory['position'][:, 2],
                'r--', label='Baseline', linewidth=2)
        ax1.set_xlabel('East (m)')
        ax1.set_ylabel('North (m)')
        ax1.set_zlabel('Up (m)')
        ax1.set_title('3D Trajectory Comparison')
        ax1.legend()
        
        # 2D trajectory plot (top view)
        ax2 = fig.add_subplot(222)
        ax2.plot(estimated_trajectory['position'][:, 1],
                estimated_trajectory['position'][:, 0],
                'b-', label='Estimated', linewidth=2)
        ax2.plot(baseline_trajectory['position'][:, 1],
                baseline_trajectory['position'][:, 0],
                'r--', label='Baseline', linewidth=2)
        ax2.set_xlabel('East (m)')
        ax2.set_ylabel('North (m)')
        ax2.set_title('2D Trajectory (Top View)')
        ax2.axis('equal')
        ax2.grid(True)
        ax2.legend()
        
        # Error plots
        if hasattr(self, 'metrics') and 'time_series' in self.metrics:
            time = self.metrics['time_series']['time']
            errors = self.metrics['time_series']['position_errors']
            
            # Position error components
            ax3 = fig.add_subplot(223)
            ax3.plot(time, errors[:, 0], 'r-', label='North')
            ax3.plot(time, errors[:, 1], 'g-', label='East')
            ax3.plot(time, errors[:, 2], 'b-', label='Down')
            ax3.set_xlabel('Time (s)')
            ax3.set_ylabel('Position Error (m)')
            ax3.set_title('Position Error Components')
            ax3.grid(True)
            ax3.legend()
            
            # 3D position error
            ax4 = fig.add_subplot(224)
            ax4.plot(time, self.metrics['time_series']['position_3d_errors'], 'k-')
            ax4.set_xlabel('Time (s)')
            ax4.set_ylabel('3D Position Error (m)')
            ax4.set_title('Total Position Error')
            ax4.grid(True)
            
            # Add RMSE annotation
            rmse_text = f"RMSE: {self.metrics['position']['3d_rmse']:.3f} m"
            ax4.text(0.95, 0.95, rmse_text, transform=ax4.transAxes,
                    ha='right', va='top', bbox=dict(boxstyle='round', facecolor='wheat'))
        
        plt.tight_layout()
        
        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            logger.info(f"📊 Trajectory comparison saved to: {os.path.abspath(output_file)}")

        # Don't show plots in background thread - just save them
        plt.close()
    
    def print_summary(self):
        """Print error statistics summary"""
        if not hasattr(self, 'metrics'):
            print("No metrics computed yet. Run compute_errors first.")
            return
        
        print("\n" + "="*60)
        print("TRAJECTORY COMPARISON SUMMARY")
        print("="*60)
        
        print(f"\nMatched points: {self.metrics['num_matched_points']}")
        
        print("\nPosition Errors:")
        print(f"  3D RMSE: {self.metrics['position']['3d_rmse']:.3f} m")
        print(f"  3D MAE:  {self.metrics['position']['3d_mae']:.3f} m")
        print(f"  3D Max:  {self.metrics['position']['3d_max']:.3f} m")
        
        print("\n  Component-wise RMSE:")
        print(f"    North: {self.metrics['position']['rmse'][0]:.3f} m")
        print(f"    East:  {self.metrics['position']['rmse'][1]:.3f} m")
        print(f"    Down:  {self.metrics['position']['rmse'][2]:.3f} m")
        
        if 'orientation' in self.metrics:
            print("\nOrientation Errors:")
            print(f"  RMSE: {self.metrics['orientation']['rmse']:.3f}°")
            print(f"  MAE:  {self.metrics['orientation']['mae']:.3f}°")
            print(f"  Max:  {self.metrics['orientation']['max']:.3f}°")
        
        print("="*60)


# Example usage function
def example_usage():
    """Example of how to use the georeferencing and comparison tools"""
    
    # 1. Initialize georeferencer
    reference_location = (36.023619907, 129.378011880, 0.0)  # Pohang reference
    georeferencer = LidarGeoreferencer(
        "path/to/extrinsics.json",
        reference_location
    )
    
    # 2. Load some example LiDAR data (you would load actual point cloud here)
    # For demo, create synthetic points
    lidar_points = np.random.randn(1000, 3) * 10  # Random points within 10m
    
    # 3. Get INS/GPS fusion results at specific timestamp
    # These would come from your fusion engine
    ins_position = np.array([10.0, 5.0, -2.0])  # NED position
    ins_orientation = np.array([1.0, 0.0, 0.0, 0.0])  # Quaternion [w,x,y,z]
    
    # 4. Georeference the point cloud
    georef_points_ned = georeferencer.georeference_point_cloud(
        lidar_points,
        ins_orientation,
        ins_position,
        sensor_name='lidar_front',
        output_frame='ned'
    )
    
    # 5. Compare trajectories
    comparator = TrajectoryComparator()
    
    # Load your fusion results and baseline
    # (You would get these from your PohangDatasetLoader)
    estimated_traj = {
        'time': np.arange(0, 100, 0.1),
        'position': np.random.randn(1000, 3).cumsum(axis=0) * 0.1,
        'orientation': np.tile([1, 0, 0, 0], (1000, 1))
    }
    
    baseline_traj = {
        'time': np.arange(0, 100, 0.1),
        'position': estimated_traj['position'] + np.random.randn(1000, 3) * 0.5,
        'quaternion': estimated_traj['orientation']
    }
    
    # Compute errors
    metrics = comparator.compute_errors(estimated_traj, baseline_traj)
    
    # Print summary
    comparator.print_summary()
    
    # Plot comparison
    comparator.plot_comparison(estimated_traj, baseline_traj, "trajectory_comparison.png")
    
    return georef_points_ned, metrics
