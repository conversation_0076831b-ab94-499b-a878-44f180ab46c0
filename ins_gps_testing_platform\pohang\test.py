
#!/usr/bin/env python3
"""
Test script for Pohang dataset processing
Run this from the main ins_gps_testing_platform directory
"""

import sys
from pathlib import Path

# Add the parent directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from pohang.pohang_integration import PohangLidarProcessor

def test_basic_functionality():
    """Test basic Pohang functionality without requiring dataset files"""
    print("="*60)
    print("TEST 1: Testing Basic Pohang Functionality")
    print("="*60)

    try:
        from pohang.pohang_analizor import PohangDatasetLoader, PohangDatasetConfig
        from pohang.lidar_georeferencing import LidarGeoreferencer
        from pohang.trajectory_comparison import TrajectoryComparator

        # Test configuration
        config = PohangDatasetConfig()
        print(f"✓ PohangDatasetConfig created with reference: ({config.ref_latitude}, {config.ref_longitude})")

        # Test coordinate transformation parameters
        print(f"✓ Earth radius: {config.earth_radius} m")
        print(f"✓ GPS accuracy: {config.gps_horizontal_accuracy} m")
        print("✓ Basic configuration working")

        # Test trajectory comparator (doesn't need files)
        comparator = TrajectoryComparator()
        print("✓ TrajectoryComparator initialized")

        return True

    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False

def test_dataset_loader():
    """Test dataset loader with actual dataset path"""
    print("\n" + "="*60)
    print("TEST 2: Testing Dataset Loader")
    print("="*60)

    dataset_path = "..\\pohang_dataset"
    print(f"Testing dataset path: {dataset_path}")

    # Check if dataset path exists
    from pathlib import Path
    if not Path(dataset_path).exists():
        print(f"! Dataset path not accessible: {dataset_path}")
        print("! This is expected if you're not connected to the network drive")
        print("✓ Skipping dataset-dependent tests")
        return True

    try:
        from pohang.pohang_analizor import PohangDatasetLoader

        # Try to initialize loader (this should work even without extrinsics.json)
        loader = PohangDatasetLoader(dataset_path)
        print("✓ PohangDatasetLoader initialized successfully")

        # Test dataset info
        info = loader.get_dataset_info()
        print(f"✓ Dataset info retrieved: {info['dataset_path']}")

        # Try to load individual data files if they exist
        navigation_dir = Path(dataset_path) / "navigation"
        if navigation_dir.exists():
            print(f"✓ Navigation directory found: {navigation_dir}")

            # Check for data files
            ahrs_file = navigation_dir / "ahrs.txt"
            gps_file = navigation_dir / "gps.txt"
            baseline_file = navigation_dir / "baseline.txt"

            if ahrs_file.exists():
                print(f"✓ AHRS file found: {ahrs_file}")
                try:
                    ahrs_data = loader.load_ahrs_data()
                    print(f"✓ AHRS data loaded: {len(ahrs_data['time'])} samples")
                except Exception as e:
                    print(f"! AHRS loading issue: {e}")

            if gps_file.exists():
                print(f"✓ GPS file found: {gps_file}")
                try:
                    gps_data = loader.load_gps_data()
                    print(f"✓ GPS data loaded: {len(gps_data['time'])} samples")
                except Exception as e:
                    print(f"! GPS loading issue: {e}")

            if baseline_file.exists():
                print(f"✓ Baseline file found: {baseline_file}")
                try:
                    baseline_data = loader.load_baseline_data()
                    print(f"✓ Baseline data loaded: {len(baseline_data['time'])} samples")
                except Exception as e:
                    print(f"! Baseline loading issue: {e}")
        else:
            print(f"! Navigation directory not found: {navigation_dir}")

        return True

    except Exception as e:
        print(f"✗ Dataset loader test failed: {e}")
        return False

def test_pohang_processing():
    """Main test function"""
    print("Starting Pohang Dataset Processing Tests")
    print("="*60)

    # Test 1: Basic functionality
    success1 = test_basic_functionality()

    # Test 2: Dataset loader
    success2 = test_dataset_loader()

    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Basic functionality: {'✓ PASS' if success1 else '✗ FAIL'}")
    print(f"Dataset loader: {'✓ PASS' if success2 else '✗ FAIL'}")

    if success1 and success2:
        print("\n✓ All core tests passed!")
        print("✓ Pohang modules are working correctly")
        print("\nNOTE: Full integration tests require:")
        print("  - Network access to dataset")
        print("  - Complete dataset files (ahrs.txt, gps.txt, baseline.txt)")
        print("  - LiDAR extrinsics.json file")
    else:
        print("\n! Some tests failed - check error messages above")

    return success1 and success2

if __name__ == "__main__":
    test_pohang_processing()