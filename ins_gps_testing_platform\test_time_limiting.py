#!/usr/bin/env python3
"""
Test script for Pohang dataset time limiting functionality
Demonstrates how to process only a subset of the data for faster analysis
"""

import sys
from pathlib import Path
import logging

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from pohang.pohang_analizor import PohangDatasetLoader
from pohang.pohang_integration import PohangLidarProcessor

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_time_limiting():
    """Test time limiting functionality"""
    
    # Dataset path (update this to your actual path)
    dataset_path = "F:/One_Drive/OneDrive - Université Laval/!Projects/Mitacs/EnvisioningLabs/codes/python/mytoolbox/generator/pohang_dataset"
    
    print("="*60)
    print("Testing Pohang Dataset Time Limiting")
    print("="*60)
    
    try:
        # Test 1: Load dataset info without time limits
        print("\n1. Loading dataset info (no time limits)...")
        loader = PohangDatasetLoader(dataset_path)
        loader.load_ahrs_data()
        loader.load_gps_data()
        loader.load_baseline_data()
        
        info = loader.get_dataset_info()
        print(f"Full dataset info:")
        for sensor, data in info.items():
            if isinstance(data, dict) and 'duration' in data:
                print(f"  {sensor}: {data['samples']} samples, {data['duration']:.1f}s duration, {data['rate']:.1f} Hz")
        
        # Test 2: Load with time limits
        print(f"\n2. Testing time limiting (300 seconds)...")
        loader_limited = PohangDatasetLoader(dataset_path)
        loader_limited.set_processing_limits(max_duration=300.0, start_offset=0.0)
        
        loader_limited.load_ahrs_data()
        loader_limited.load_gps_data()
        loader_limited.load_baseline_data()
        
        info_limited = loader_limited.get_dataset_info()
        print(f"Limited dataset info:")
        for sensor, data in info_limited.items():
            if isinstance(data, dict) and 'duration' in data:
                print(f"  {sensor}: {data['samples']} samples, {data['duration']:.1f}s duration, {data['rate']:.1f} Hz")
        
        # Test 3: Compare processing times (simulation)
        print(f"\n3. Estimated processing time comparison:")
        
        if 'ahrs' in info and 'ahrs' in info_limited:
            full_samples = info['ahrs']['samples']
            limited_samples = info_limited['ahrs']['samples']
            reduction_factor = limited_samples / full_samples
            
            print(f"  Full dataset: ~{full_samples} IMU samples")
            print(f"  Limited dataset: ~{limited_samples} IMU samples")
            print(f"  Reduction factor: {reduction_factor:.2f} ({reduction_factor*100:.1f}%)")
            print(f"  Estimated speedup: {1/reduction_factor:.1f}x faster")
        
        # Test 4: Different time windows
        print(f"\n4. Testing different time windows...")
        
        test_durations = [60, 180, 300, 600]  # 1, 3, 5, 10 minutes
        
        for duration in test_durations:
            loader_test = PohangDatasetLoader(dataset_path)
            loader_test.set_processing_limits(max_duration=duration, start_offset=0.0)
            loader_test.load_ahrs_data()
            
            if loader_test.ahrs_data:
                samples = len(loader_test.ahrs_data['time'])
                actual_duration = loader_test.ahrs_data['time'][-1] - loader_test.ahrs_data['time'][0]
                print(f"  {duration}s limit: {samples} samples, {actual_duration:.1f}s actual duration")
        
        print(f"\n✓ Time limiting tests completed successfully!")
        print(f"\nRecommendations:")
        print(f"  - For quick testing: 60-300 seconds (1-5 minutes)")
        print(f"  - For development: 300-600 seconds (5-10 minutes)")
        print(f"  - For final analysis: Full dataset (remove time limits)")
        
    except Exception as e:
        print(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

def test_fusion_with_time_limits():
    """Test actual fusion processing with time limits"""
    
    dataset_path = "F:/One_Drive/OneDrive - Université Laval/!Projects/Mitacs/EnvisioningLabs/codes/python/mytoolbox/generator/pohang_dataset"
    
    print("\n" + "="*60)
    print("Testing Fusion Processing with Time Limits")
    print("="*60)
    
    try:
        # Initialize processor
        processor = PohangLidarProcessor(dataset_path)
        
        # Set time limits (5 minutes for testing)
        processor.dataset_loader.set_processing_limits(max_duration=300.0, start_offset=0.0)
        
        print("Running fusion with 5-minute time limit...")
        import time
        start_time = time.time()
        
        # Run fusion
        results = processor.run_ins_gps_fusion()
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"✓ Fusion completed in {processing_time:.1f} seconds")
        print(f"  Processed {len(results['time'])} time steps")
        print(f"  Data duration: {results['time'][-1] - results['time'][0]:.1f} seconds")
        
        # Compare with baseline if available
        try:
            metrics = processor.compare_with_baseline()
            if metrics:
                print(f"✓ Baseline comparison completed")
                print(f"  RMS error: {metrics.get('rms_error_3d', 'N/A')}")
        except:
            print("! Baseline comparison not available")
        
    except Exception as e:
        print(f"Error during fusion testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_time_limiting()
    
    # Uncomment to test actual fusion processing
    # test_fusion_with_time_limits()
