"""Coordinate Transformation Utilities"""
import numpy as np
from typing import Tuple
from utils.constants import EARTH_RADIUS, EARTH_FLATTENING

def lla_to_ned(lla: np.ndarray, ref_lla: Tuple[float, float, float]) -> np.ndarray:
    """
    Convert LLA coordinates to NED frame
    
    Args:
        lla: [N x 3] array of [lat, lon, alt] in degrees and meters
        ref_lla: Reference LLA (lat, lon, alt) in degrees and meters
        
    Returns:
        [N x 3] array of NED positions in meters
    """
    lat_rad = np.radians(lla[:, 0])
    lon_rad = np.radians(lla[:, 1])
    alt = lla[:, 2]
    
    ref_lat_rad = np.radians(ref_lla[0])
    ref_lon_rad = np.radians(ref_lla[1])
    ref_alt = ref_lla[2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Radius of curvature
    N = a / np.sqrt(1 - e2 * np.sin(ref_lat_rad)**2)
    M = a * (1 - e2) / (1 - e2 * np.sin(ref_lat_rad)**2)**(3/2)
    
    # Convert to NED
    dlat = lat_rad - ref_lat_rad
    dlon = lon_rad - ref_lon_rad
    dalt = alt - ref_alt
    
    north = dlat * (M + ref_alt)
    east = dlon * (N + ref_alt) * np.cos(ref_lat_rad)
    down = -dalt
    
    return np.column_stack([north, east, down])

def ned_to_lla(ned: np.ndarray, ref_lla: Tuple[float, float, float]) -> np.ndarray:
    """
    Convert NED coordinates to LLA frame
    
    Args:
        ned: [N x 3] array of NED positions in meters  
        ref_lla: Reference LLA (lat, lon, alt) in degrees and meters
        
    Returns:
        [N x 3] array of [lat, lon, alt] in degrees and meters
    """
    north = ned[:, 0]
    east = ned[:, 1]
    down = ned[:, 2]
    
    ref_lat_rad = np.radians(ref_lla[0])
    ref_lon_rad = np.radians(ref_lla[1])
    ref_alt = ref_lla[2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Radius of curvature
    N = a / np.sqrt(1 - e2 * np.sin(ref_lat_rad)**2)
    M = a * (1 - e2) / (1 - e2 * np.sin(ref_lat_rad)**2)**(3/2)
    
    # Convert to LLA
    dlat = north / (M + ref_alt)
    dlon = east / ((N + ref_alt) * np.cos(ref_lat_rad))
    dalt = -down
    
    lat = np.degrees(ref_lat_rad + dlat)
    lon = np.degrees(ref_lon_rad + dlon)
    alt = ref_alt + dalt
    
    return np.column_stack([lat, lon, alt])

def ecef_to_lla(ecef: np.ndarray) -> np.ndarray:
    """
    Convert ECEF coordinates to LLA
    
    Args:
        ecef: [N x 3] array of ECEF coordinates in meters
        
    Returns:
        [N x 3] array of [lat, lon, alt] in degrees and meters
    """
    x = ecef[:, 0]
    y = ecef[:, 1]
    z = ecef[:, 2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Longitude
    lon = np.arctan2(y, x)
    
    # Latitude and altitude (iterative)
    p = np.sqrt(x**2 + y**2)
    lat = np.arctan2(z, p * (1 - e2))
    
    # Iterate for better accuracy
    for _ in range(3):
        N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
        alt = p / np.cos(lat) - N
        lat = np.arctan2(z, p * (1 - e2 * N / (N + alt)))
    
    # Final altitude calculation
    N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
    alt = p / np.cos(lat) - N
    
    return np.column_stack([np.degrees(lat), np.degrees(lon), alt])

def lla_to_ecef(lla: np.ndarray) -> np.ndarray:
    """
    Convert LLA coordinates to ECEF
    
    Args:
        lla: [N x 3] array of [lat, lon, alt] in degrees and meters
        
    Returns:
        [N x 3] array of ECEF coordinates in meters
    """
    lat_rad = np.radians(lla[:, 0])
    lon_rad = np.radians(lla[:, 1])
    alt = lla[:, 2]
    
    # Earth model parameters
    a = EARTH_RADIUS
    f = EARTH_FLATTENING
    e2 = 2*f - f**2
    
    # Radius of curvature in prime vertical
    N = a / np.sqrt(1 - e2 * np.sin(lat_rad)**2)
    
    # ECEF coordinates
    x = (N + alt) * np.cos(lat_rad) * np.cos(lon_rad)
    y = (N + alt) * np.cos(lat_rad) * np.sin(lon_rad)
    z = (N * (1 - e2) + alt) * np.sin(lat_rad)
    
    return np.column_stack([x, y, z])

def ned_to_body(ned_vector: np.ndarray, quaternion: np.ndarray) -> np.ndarray:
    """
    Transform vector from NED to body frame using quaternion
    
    Args:
        ned_vector: Vector in NED frame
        quaternion: Quaternion [w, x, y, z] representing NED to body rotation
        
    Returns:
        Vector in body frame
    """
    from scipy.spatial.transform import Rotation
    
    # Convert quaternion format for scipy
    r = Rotation.from_quat([quaternion[1], quaternion[2], quaternion[3], quaternion[0]])
    
    return r.apply(ned_vector, inverse=True)

def body_to_ned(body_vector: np.ndarray, quaternion: np.ndarray) -> np.ndarray:
    """
    Transform vector from body to NED frame using quaternion
    
    Args:
        body_vector: Vector in body frame
        quaternion: Quaternion [w, x, y, z] representing NED to body rotation
        
    Returns:
        Vector in NED frame
    """
    from scipy.spatial.transform import Rotation
    
    # Convert quaternion format for scipy
    r = Rotation.from_quat([quaternion[1], quaternion[2], quaternion[3], quaternion[0]])
    
    return r.apply(body_vector)

def compute_local_gravity(latitude: float, altitude: float = 0.0) -> float:
    """
    Compute local gravity magnitude
    
    Args:
        latitude: Latitude in degrees
        altitude: Altitude in meters above sea level
        
    Returns:
        Gravity magnitude in m/s²
    """
    lat_rad = np.radians(latitude)
    
    # WGS84 gravity formula
    g0 = 9.7803253359  # Equatorial gravity
    g1 = 0.0053024     # Latitude correction
    g2 = 0.0000058     # Latitude squared correction
    
    # Sea level gravity
    g_sea = g0 * (1 + g1 * np.sin(lat_rad)**2 - g2 * np.sin(2*lat_rad)**2)
    
    # Altitude correction
    g_alt = g_sea * (1 - 2 * altitude / EARTH_RADIUS)
    
    return g_alt
