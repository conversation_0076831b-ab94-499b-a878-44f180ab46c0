"""Pohang Dataset Processing Tab"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
import threading
from pathlib import Path
import sys
import pandas as pd
import numpy as np
import os

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from pohang.pohang_integration import PohangLidarProcessor
from preprocessing.marine_imu_preprocessor import PreprocessingConfig

logger = logging.getLogger(__name__)

class PohangTab:
    """Tab for processing Pohang Canal Dataset"""
    
    def __init__(self, parent_notebook, main_gui):
        self.parent_notebook = parent_notebook
        self.main_gui = main_gui
        
        # Create main frame
        self.frame = ttk.Frame(parent_notebook)
        
        # Initialize variables
        self.dataset_path = tk.StringVar()
        self.process_lidar = tk.BooleanVar(value=True)
        self.compare_baseline = tk.BooleanVar(value=True)
        self.visualize_results = tk.BooleanVar(value=True)
        self.use_imu_preprocessing = tk.BooleanVar(value=True)  # Default enabled

        # IMU preprocessing filter variables
        self.apply_spike_removal = tk.BooleanVar(value=True)
        self.spike_kernel_size = tk.StringVar(value="5")
        self.apply_low_pass = tk.BooleanVar(value=True)
        self.low_pass_cutoff = tk.StringVar(value="8.0")
        self.apply_smoothing = tk.BooleanVar(value=True)
        self.smoothing_window = tk.StringVar(value="5")

        # Time limiting variables
        self.use_time_limit = tk.BooleanVar(value=True)
        self.max_duration = tk.StringVar(value="300")  # Default 5 minutes
        self.start_offset = tk.StringVar(value="800")
        
        # Processing state
        self.processor = None
        self.processing_thread = None
        self.is_processing = False
        
        self.create_widgets()

    def _toggle_time_limit_controls(self):
        """Enable/disable time limit controls based on checkbox"""
        state = tk.NORMAL if self.use_time_limit.get() else tk.DISABLED
        for child in self.time_controls_frame.winfo_children():
            for widget in child.winfo_children():
                if isinstance(widget, ttk.Entry):
                    widget.configure(state=state)

    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Main container with scrollbar
        canvas = tk.Canvas(self.frame)
        scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Dataset Configuration Section
        dataset_frame = ttk.LabelFrame(scrollable_frame, text="Dataset Configuration", padding=10)
        dataset_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Dataset path selection
        ttk.Label(dataset_frame, text="Dataset Path:").grid(row=0, column=0, sticky=tk.W, pady=2)
        path_frame = ttk.Frame(dataset_frame)
        path_frame.grid(row=0, column=1, columnspan=2, sticky=tk.EW, pady=2)
        
        self.path_entry = ttk.Entry(path_frame, textvariable=self.dataset_path, width=60)
        self.path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(path_frame, text="Browse", command=self.browse_dataset).pack(side=tk.RIGHT, padx=(5, 0))
        
        # Set default path
        self.dataset_path.set("F:/One_Drive/OneDrive - Université Laval/!Projects/Mitacs/EnvisioningLabs/codes/python/mytoolbox/generator/pohang_dataset_005")
        
        # Note: Sampling rate removed - EKF now handles discrete measurements with original timestamps
        
        # Processing options
        options_frame = ttk.LabelFrame(scrollable_frame, text="Processing Options", padding=10)
        options_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Checkbutton(options_frame, text="Process LiDAR data",
                       variable=self.process_lidar).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(options_frame, text="Compare with baseline",
                       variable=self.compare_baseline).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(options_frame, text="Generate visualizations",
                       variable=self.visualize_results).pack(anchor=tk.W, pady=2)
        ttk.Checkbutton(options_frame, text="Apply IMU preprocessing filters (recommended)",
                       variable=self.use_imu_preprocessing).pack(anchor=tk.W, pady=2)

        # IMU Preprocessing Filter Controls
        filter_frame = ttk.LabelFrame(scrollable_frame, text="IMU Preprocessing Filters", padding=10)
        filter_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Checkbutton(filter_frame, text="Enable IMU Preprocessing",
                       variable=self.use_imu_preprocessing).grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=2)

        # Spike Removal
        ttk.Checkbutton(filter_frame, text="Apply Spike Removal (Median Filter)",
                       variable=self.apply_spike_removal).grid(row=1, column=0, sticky=tk.W, pady=2)
        spike_frame = ttk.Frame(filter_frame)
        spike_frame.grid(row=1, column=1, sticky=tk.W, padx=10)
        ttk.Label(spike_frame, text="Kernel size:").pack(side=tk.LEFT)
        ttk.Entry(spike_frame, textvariable=self.spike_kernel_size, width=5).pack(side=tk.LEFT, padx=2)

        # Low-Pass Filter
        ttk.Checkbutton(filter_frame, text="Apply Low-Pass Filter (Butterworth)",
                       variable=self.apply_low_pass).grid(row=2, column=0, sticky=tk.W, pady=2)
        lowpass_frame = ttk.Frame(filter_frame)
        lowpass_frame.grid(row=2, column=1, sticky=tk.W, padx=10)
        ttk.Label(lowpass_frame, text="Cutoff (Hz):").pack(side=tk.LEFT)
        ttk.Entry(lowpass_frame, textvariable=self.low_pass_cutoff, width=5).pack(side=tk.LEFT, padx=2)

        # Smoothing
        ttk.Checkbutton(filter_frame, text="Apply Smoothing (Moving Average)",
                       variable=self.apply_smoothing).grid(row=3, column=0, sticky=tk.W, pady=2)
        smooth_frame = ttk.Frame(filter_frame)
        smooth_frame.grid(row=3, column=1, sticky=tk.W, padx=10)
        ttk.Label(smooth_frame, text="Window size:").pack(side=tk.LEFT)
        ttk.Entry(smooth_frame, textvariable=self.smoothing_window, width=5).pack(side=tk.LEFT, padx=2)

        # Time limiting controls
        ttk.Separator(options_frame, orient='horizontal').pack(fill=tk.X, pady=5)

        time_limit_frame = ttk.Frame(options_frame)
        time_limit_frame.pack(fill=tk.X, pady=2)

        ttk.Checkbutton(time_limit_frame, text="Limit processing time (for faster analysis)",
                       variable=self.use_time_limit,
                       command=self._toggle_time_limit_controls).pack(anchor=tk.W)

        # Time limit controls (initially disabled)
        self.time_controls_frame = ttk.Frame(options_frame)
        self.time_controls_frame.pack(fill=tk.X, padx=20, pady=2)

        duration_frame = ttk.Frame(self.time_controls_frame)
        duration_frame.pack(fill=tk.X, pady=1)
        ttk.Label(duration_frame, text="Max duration (seconds):").pack(side=tk.LEFT)
        ttk.Entry(duration_frame, textvariable=self.max_duration, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Label(duration_frame, text="(e.g., 300 = 5 minutes)").pack(side=tk.LEFT, padx=5)

        offset_frame = ttk.Frame(self.time_controls_frame)
        offset_frame.pack(fill=tk.X, pady=1)
        ttk.Label(offset_frame, text="Start offset (seconds):").pack(side=tk.LEFT)
        ttk.Entry(offset_frame, textvariable=self.start_offset, width=10).pack(side=tk.LEFT, padx=5)
        ttk.Label(offset_frame, text="(0 = start from beginning)").pack(side=tk.LEFT, padx=5)

        # Initially disable time controls
        self._toggle_time_limit_controls()
        
        # Control Buttons Section
        control_frame = ttk.LabelFrame(scrollable_frame, text="Processing Control", padding=10)
        control_frame.pack(fill=tk.X, padx=10, pady=5)
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.process_button = ttk.Button(button_frame, text="Start Processing", 
                                       command=self.start_processing)
        self.process_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.stop_button = ttk.Button(button_frame, text="Stop Processing", 
                                    command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Clear Log", 
                  command=self.clear_log).pack(side=tk.RIGHT)
        
        # Results/Log Section
        log_frame = ttk.LabelFrame(scrollable_frame, text="Processing Log", padding=10)
        log_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Create text widget with scrollbar
        text_frame = ttk.Frame(log_frame)
        text_frame.pack(fill=tk.BOTH, expand=True)
        
        self.log_text = tk.Text(text_frame, height=15, wrap=tk.WORD)
        log_scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=log_scrollbar.set)
        
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Pack main components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # Configure grid weights
        dataset_frame.columnconfigure(1, weight=1)
        
        self.log_message("Pohang Dataset Processing Tab initialized")
        self.log_message("Ready to process Pohang Canal Dataset")
        
    def browse_dataset(self):
        """Browse for dataset directory"""
        directory = filedialog.askdirectory(
            title="Select Pohang Dataset Directory",
            initialdir=self.dataset_path.get() if self.dataset_path.get() else "/"
        )
        if directory:
            self.dataset_path.set(directory)
            self.log_message(f"Dataset path set to: {directory}")
    
    def log_message(self, message: str):
        """Add message to log"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.log_text.update_idletasks()
        logger.info(message)
    
    def clear_log(self):
        """Clear the log text"""
        self.log_text.delete(1.0, tk.END)
    
    def start_processing(self):
        """Start Pohang dataset processing in a separate thread"""
        if self.is_processing:
            messagebox.showwarning("Processing", "Processing is already in progress!")
            return
        
        # Validate inputs
        if not self.dataset_path.get().strip():
            messagebox.showerror("Error", "Please select a dataset path!")
            return
        
        if not Path(self.dataset_path.get()).exists():
            messagebox.showerror("Error", f"Dataset path does not exist: {self.dataset_path.get()}")
            return
        
        # Update UI state
        self.is_processing = True
        self.process_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.main_gui.update_status("Processing Pohang dataset...")
        
        # Start processing in separate thread
        self.processing_thread = threading.Thread(target=self.run_processing, daemon=True)
        self.processing_thread.start()
    
    def run_processing(self):
        """Run the actual processing (in separate thread)"""
        try:
            self.log_message("="*60)
            self.log_message("Starting Pohang Dataset Processing")
            self.log_message("="*60)
            
            # Create the configuration object from the GUI
            preprocess_config = PreprocessingConfig(
                apply_preprocessing=self.use_imu_preprocessing.get(),
                apply_spike_removal=self.apply_spike_removal.get(),
                spike_kernel_size=int(self.spike_kernel_size.get()),
                apply_low_pass=self.apply_low_pass.get(),
                low_pass_cutoff_hz=float(self.low_pass_cutoff.get()),
                apply_smoothing=self.apply_smoothing.get(),
                smoothing_window_size=int(self.smoothing_window.get())
            )

            # Initialize processor
            self.log_message(f"Initializing processor with dataset: {self.dataset_path.get()}")
            self.processor = PohangLidarProcessor(
                self.dataset_path.get(),
                preprocess_config=preprocess_config
            )
            preprocessing_status = "enabled" if self.use_imu_preprocessing.get() else "disabled"
            self.log_message(f"✓ Processor initialized successfully (IMU preprocessing: {preprocessing_status})")

            # Apply time limits if enabled
            if self.use_time_limit.get():
                try:
                    max_duration = float(self.max_duration.get())
                    start_offset = float(self.start_offset.get())
                    self.processor.dataset_loader.set_processing_limits(max_duration, start_offset)
                    self.log_message(f"✓ Applied time limits: {max_duration}s duration, {start_offset}s offset")
                except ValueError as e:
                    self.log_message(f"! Invalid time limit values: {e}")
                    self.log_message("! Proceeding without time limits")
            else:
                self.log_message("Processing all available data (no time limits)")

            # Run INS/GPS fusion
            self.log_message("Running INS/GPS fusion...")
            self.main_gui.update_progress(20)
            
            fusion_results = self.processor.run_ins_gps_fusion()
            self.log_message("✓ INS/GPS fusion completed")
            self.main_gui.update_progress(50)

            # Initialize metrics variable
            metrics = None

            # Compare with baseline if requested
            if self.compare_baseline.get():
                self.log_message("Comparing with baseline trajectory...")
                try:
                    metrics = self.processor.compare_with_baseline()
                    if metrics:
                        self.log_message("✓ Baseline comparison completed")
                    else:
                        self.log_message("! No baseline data available for comparison")
                except Exception as e:
                    self.log_message(f"! Baseline comparison failed: {e}")
                    metrics = None
            
            self.main_gui.update_progress(75)
            
            # Generate visualizations if requested
            if self.visualize_results.get():
                self.log_message("Generating visualizations...")
                try:
                    self.processor.visualize_results(
                        show_lidar_trajectory=self.process_lidar.get(),
                        save_plots=True,
                        output_dir="../exports/pohang_results"
                    )
                    self.log_message("✓ Visualizations completed")
                except Exception as e:
                    self.log_message(f"! Visualization failed: {e}")
            
            # Export data to CSV files for visualization tab
            self.log_message("Exporting data to CSV files...")
            try:
                self.export_results_to_csv(fusion_results, metrics)
                self.log_message("✓ CSV data files exported successfully")
            except Exception as e:
                self.log_message(f"! CSV export failed: {e}")

            self.main_gui.update_progress(100)
            self.log_message("="*60)
            self.log_message("✓ Processing completed successfully!")
            self.log_message("📁 Results saved to: ../exports/pohang_results/")
            self.log_message("📊 Check the exports folder for comprehensive visualization files:")
            self.log_message("   • estimated_trajectory_only.png - Your fusion results")
            self.log_message("   • ground_truth_trajectory_only.png - Baseline trajectory")
            self.log_message("   • comprehensive_trajectory_comparison.png - Complete comparison")
            self.log_message("   • detailed_error_analysis.png - Comprehensive error analysis")
            self.log_message("   • coordinate_frame_alignment.png - Frame alignment verification")
            self.log_message("   • sensor_trajectories.png - Vehicle and sensor paths")
            self.log_message("📄 CSV data files for interactive visualization:")
            self.log_message("   • trajectory_data.csv - Estimated and baseline trajectories")
            self.log_message("   • error_metrics.csv - Position errors and statistics")
            self.log_message("   • coordinate_frames.csv - Coordinate transformation data")
            self.log_message("🎯 All trajectories displayed in same Local Geodetic Frame (LGF)")
            self.log_message("📈 Error analysis includes RMS, statistics, and distribution plots")
            self.log_message("="*60)
            
        except Exception as e:
            self.log_message(f"✗ Processing failed: {str(e)}")
            logger.error(f"Pohang processing error: {e}", exc_info=True)
        
        finally:
            # Reset UI state
            self.is_processing = False
            self.process_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)
            self.main_gui.update_status("Ready")
            self.main_gui.update_progress(0)
    
    def stop_processing(self):
        """Stop processing (placeholder - actual implementation would need thread coordination)"""
        self.log_message("Stop requested - processing will complete current step...")
        self.is_processing = False
        self.stop_button.config(state=tk.DISABLED)

    def export_results_to_csv(self, fusion_results, metrics):
        """Export processing results to CSV files for visualization tab"""
        try:
            # Create output directory
            output_dir = Path("../exports/pohang_results")
            output_dir.mkdir(parents=True, exist_ok=True)

            # 1. Export trajectory data
            if fusion_results and hasattr(self.processor, 'baseline_data') and self.processor.baseline_data:
                self.export_trajectory_data(fusion_results, self.processor.baseline_data, output_dir)

            # 2. Export raw GPS data (for visualization)
            self.export_gps_data(output_dir)

            # 3. Export error metrics
            if metrics:
                self.export_error_metrics(metrics, output_dir)

            # 4. Export coordinate frame data
            if fusion_results and hasattr(self.processor, 'baseline_data') and self.processor.baseline_data:
                self.export_coordinate_frames(fusion_results, self.processor.baseline_data, output_dir)

        except Exception as e:
            logger.error(f"CSV export error: {e}", exc_info=True)
            raise

    def export_gps_data(self, output_dir):
        """Export GPS data with same coordinate alignment as other trajectories"""
        try:
            if hasattr(self.processor.dataset_loader, 'gps_data') and self.processor.dataset_loader.gps_data:
                gps_data = self.processor.dataset_loader.gps_data

                # Get GPS position data
                gps_position = None
                if 'position_ned' in gps_data:
                    gps_position = gps_data['position_ned']
                elif 'position' in gps_data:
                    gps_position = gps_data['position']

                if gps_position is not None:
                    # Apply same coordinate alignment as estimated trajectory
                    # (to ensure GPS data appears in same coordinate system as other trajectories)
                    if hasattr(self.processor, 'baseline_data') and self.processor.baseline_data:
                        baseline_pos = self.processor.baseline_data['position']
                        gps_center = np.mean(gps_position, axis=0)

                        # COORDINATE SYSTEM FIX: Baseline position is [Easting, Northing, Down]
                        # but we need [North, East, Down] for alignment with GPS NED coordinates
                        baseline_pos_ned = np.column_stack([
                            baseline_pos[:, 1],  # Northing (y) -> North
                            baseline_pos[:, 0],  # Easting (x) -> East
                            baseline_pos[:, 2]   # Down (z) -> Down
                        ])
                        baseline_center = np.mean(baseline_pos_ned, axis=0)
                        gps_pos_aligned = gps_position - gps_center + baseline_center

                        self.log_message(f"GPS coordinate alignment applied:")
                        self.log_message(f"  GPS center: [{gps_center[0]:.2f}, {gps_center[1]:.2f}, {gps_center[2]:.2f}]")
                        self.log_message(f"  Baseline center: [{baseline_center[0]:.2f}, {baseline_center[1]:.2f}, {baseline_center[2]:.2f}]")
                        self.log_message(f"  GPS translation: [{baseline_center[0]-gps_center[0]:.2f}, {baseline_center[1]-gps_center[1]:.2f}, {baseline_center[2]-gps_center[2]:.2f}]")
                    else:
                        gps_pos_aligned = gps_position
                        self.log_message("No baseline data - using original GPS coordinates")

                    # Create GPS data dictionary with aligned coordinates
                    gps_export_data = {
                        'time': gps_data['time'],
                        'gps_north': gps_pos_aligned[:, 0],  # Aligned GPS coordinates
                        'gps_east': gps_pos_aligned[:, 1],
                        'gps_down': gps_pos_aligned[:, 2],
                    }

                    # Add additional GPS info if available
                    if 'velocity' in gps_data:
                        gps_export_data.update({
                            'gps_vel_north': gps_data['velocity'][:, 0],
                            'gps_vel_east': gps_data['velocity'][:, 1],
                            'gps_vel_down': gps_data['velocity'][:, 2],
                        })

                    # Add lat/lon if available (original coordinates)
                    if 'latitude' in gps_data and 'longitude' in gps_data:
                        gps_export_data.update({
                            'gps_latitude': gps_data['latitude'],
                            'gps_longitude': gps_data['longitude'],
                        })

                    # Save to CSV
                    gps_df = pd.DataFrame(gps_export_data)
                    gps_file = output_dir / "gps_data.csv"
                    gps_df.to_csv(gps_file, index=False)
                    logger.info(f"GPS data exported with coordinate alignment to: {gps_file}")

        except Exception as e:
            logger.error(f"Error exporting GPS data: {e}")
            # Don't raise - GPS export is optional

    def export_trajectory_data(self, fusion_results, baseline_data, output_dir):
        """Export trajectory data to CSV using relative timestamps for consistency with trajectory comparison"""
        try:
            # Apply coordinate alignment for visualization
            est_pos_original = fusion_results['position']

            # Align estimated trajectory with baseline coordinate system (for visualization)
            if baseline_data and 'position' in baseline_data:
                baseline_pos = baseline_data['position']
                est_center = np.mean(est_pos_original, axis=0)

                # COORDINATE SYSTEM FIX: Baseline position is [Easting, Northing, Down]
                # but estimated trajectory is in [North, East, Down] format
                baseline_pos_ned = np.column_stack([
                    baseline_pos[:, 1],  # Northing (y) -> North
                    baseline_pos[:, 0],  # Easting (x) -> East
                    baseline_pos[:, 2]   # Down (z) -> Down
                ])
                baseline_center = np.mean(baseline_pos_ned, axis=0)
                est_pos_aligned = est_pos_original - est_center + baseline_center

                self.log_message(f"Coordinate alignment applied:")
                self.log_message(f"  Estimated center: [{est_center[0]:.2f}, {est_center[1]:.2f}, {est_center[2]:.2f}]")
                self.log_message(f"  Baseline center: [{baseline_center[0]:.2f}, {baseline_center[1]:.2f}, {baseline_center[2]:.2f}]")
                self.log_message(f"  Translation: [{baseline_center[0]-est_center[0]:.2f}, {baseline_center[1]-est_center[1]:.2f}, {baseline_center[2]-est_center[2]:.2f}]")
            else:
                est_pos_aligned = est_pos_original
                self.log_message("No baseline data available - using original estimated coordinates")

            # Convert absolute timestamps to relative timestamps
            absolute_times = fusion_results['time']
            relative_times = absolute_times - absolute_times[0]  # Start from t=0

            self.log_message(f"Converting timestamps to relative time:")
            self.log_message(f"  Original time range: {absolute_times[0]:.1f} to {absolute_times[-1]:.1f} (Unix time)")
            self.log_message(f"  Relative time range: {relative_times[0]:.1f} to {relative_times[-1]:.1f} seconds")

            # Prepare estimated trajectory data (using aligned coordinates and relative time)
            est_data = {
                'time': relative_times,  # Use relative time instead of absolute Unix time
                'absolute_time': absolute_times,  # Keep absolute time for reference
                'est_north': est_pos_aligned[:, 0],
                'est_east': est_pos_aligned[:, 1],
                'est_down': est_pos_aligned[:, 2],
                'est_vel_north': fusion_results['velocity'][:, 0],
                'est_vel_east': fusion_results['velocity'][:, 1],
                'est_vel_down': fusion_results['velocity'][:, 2],
            }

            # Add orientation data if available
            if 'orientation' in fusion_results:
                est_data.update({
                    'est_quat_w': fusion_results['orientation'][:, 0],
                    'est_quat_x': fusion_results['orientation'][:, 1],
                    'est_quat_y': fusion_results['orientation'][:, 2],
                    'est_quat_z': fusion_results['orientation'][:, 3],
                })

            # Create DataFrame for estimated trajectory
            est_df = pd.DataFrame(est_data)

            # Prepare baseline trajectory data
            if baseline_data and 'time' in baseline_data:
                # Convert baseline absolute timestamps to relative timestamps
                baseline_absolute_times = baseline_data['time']
                baseline_relative_times = baseline_absolute_times - baseline_absolute_times[0]  # Start from t=0

                # COORDINATE SYSTEM FIX: Baseline data is in UTM format where:
                # position[:, 0] = x coordinate = Easting
                # position[:, 1] = y coordinate = Northing
                # position[:, 2] = z coordinate = Down
                base_data = {
                    'time': baseline_relative_times,  # Use relative time for consistency
                    'absolute_time': baseline_absolute_times,  # Keep absolute time for reference
                    'base_north': baseline_data['position'][:, 1],  # y coordinate = Northing
                    'base_east': baseline_data['position'][:, 0],   # x coordinate = Easting
                    'base_down': baseline_data['position'][:, 2],   # z coordinate = Down
                }

                # Add quaternion data if available
                if 'quaternion' in baseline_data:
                    base_data.update({
                        'base_quat_w': baseline_data['quaternion'][:, 0],
                        'base_quat_x': baseline_data['quaternion'][:, 1],
                        'base_quat_y': baseline_data['quaternion'][:, 2],
                        'base_quat_z': baseline_data['quaternion'][:, 3],
                    })

                base_df = pd.DataFrame(base_data)

                # Merge trajectories on time (using nearest neighbor)
                merged_df = pd.merge_asof(est_df.sort_values('time'),
                                       base_df.sort_values('time'),
                                       on='time',
                                       direction='nearest',
                                       tolerance=0.1)  # 0.1 second tolerance
            else:
                merged_df = est_df

            # Save to CSV
            trajectory_file = output_dir / "trajectory_data.csv"
            merged_df.to_csv(trajectory_file, index=False)
            logger.info(f"Trajectory data exported to: {trajectory_file}")

        except Exception as e:
            logger.error(f"Error exporting trajectory data: {e}")
            raise

    def export_error_metrics(self, metrics, output_dir):
        """Export error metrics to CSV using relative timestamps from GPS data"""
        try:
            # Prepare error data
            error_data = []

            # Time series errors
            if 'time_series' in metrics:
                time_series = metrics['time_series']

                # Convert absolute timestamps to relative timestamps
                # Use the first timestamp as reference (t=0)
                absolute_times = time_series['time']
                relative_times = absolute_times - absolute_times[0]  # Start from t=0

                for i, relative_t in enumerate(relative_times):
                    row = {
                        'time': relative_t,  # Use relative time instead of absolute Unix time
                        'absolute_time': absolute_times[i],  # Keep absolute time for reference
                        'position_error_north': time_series['position_errors'][i, 0],
                        'position_error_east': time_series['position_errors'][i, 1],
                        'position_error_down': time_series['position_errors'][i, 2],
                        'position_error_3d': time_series['position_3d_errors'][i],
                    }

                    # Add orientation errors if available
                    if 'orientation_errors' in time_series:
                        row['orientation_error_deg'] = time_series['orientation_errors'][i]

                    error_data.append(row)

            # Create DataFrame
            error_df = pd.DataFrame(error_data)

            # Add summary statistics as additional rows
            if 'position' in metrics:
                pos_metrics = metrics['position']
                summary_data = {
                    'metric_type': ['rmse_north', 'rmse_east', 'rmse_down', 'rmse_3d',
                                   'mae_north', 'mae_east', 'mae_down', 'mae_3d',
                                   'max_north', 'max_east', 'max_down', 'max_3d'],
                    'value': [
                        pos_metrics['rmse'][0], pos_metrics['rmse'][1], pos_metrics['rmse'][2], pos_metrics['3d_rmse'],
                        pos_metrics['mae'][0], pos_metrics['mae'][1], pos_metrics['mae'][2], pos_metrics['3d_mae'],
                        pos_metrics['max'][0], pos_metrics['max'][1], pos_metrics['max'][2], pos_metrics['3d_max']
                    ]
                }

                # Add orientation metrics if available
                if 'orientation' in metrics:
                    orient_metrics = metrics['orientation']
                    summary_data['metric_type'].extend(['orientation_rmse_deg', 'orientation_mae_deg', 'orientation_max_deg'])
                    summary_data['value'].extend([orient_metrics['rmse'], orient_metrics['mae'], orient_metrics['max']])

                summary_df = pd.DataFrame(summary_data)

                # Save both files
                error_file = output_dir / "error_metrics.csv"
                error_df.to_csv(error_file, index=False)

                summary_file = output_dir / "error_summary.csv"
                summary_df.to_csv(summary_file, index=False)

                logger.info(f"Error metrics exported to: {error_file}")
                logger.info(f"Error summary exported to: {summary_file}")

        except Exception as e:
            logger.error(f"Error exporting error metrics: {e}")
            raise

    def export_coordinate_frames(self, fusion_results, baseline_data, output_dir):
        """Export coordinate frame information to CSV"""
        try:
            # Calculate coordinate frame statistics
            coord_data = []

            # Apply same coordinate alignment as in trajectory export
            est_pos_original = fusion_results['position']
            if baseline_data and 'position' in baseline_data:
                baseline_pos = baseline_data['position']
                est_center = np.mean(est_pos_original, axis=0)

                # COORDINATE SYSTEM FIX: Convert baseline from UTM [Easting, Northing, Down] to NED
                baseline_pos_ned = np.column_stack([
                    baseline_pos[:, 1],  # Northing (y) -> North
                    baseline_pos[:, 0],  # Easting (x) -> East
                    baseline_pos[:, 2]   # Down (z) -> Down
                ])
                baseline_center = np.mean(baseline_pos_ned, axis=0)
                est_pos = est_pos_original - est_center + baseline_center
            else:
                est_pos = est_pos_original
            est_stats = {
                'frame_type': 'estimated',
                'north_min': np.min(est_pos[:, 0]),
                'north_max': np.max(est_pos[:, 0]),
                'north_mean': np.mean(est_pos[:, 0]),
                'east_min': np.min(est_pos[:, 1]),
                'east_max': np.max(est_pos[:, 1]),
                'east_mean': np.mean(est_pos[:, 1]),
                'down_min': np.min(est_pos[:, 2]),
                'down_max': np.max(est_pos[:, 2]),
                'down_mean': np.mean(est_pos[:, 2]),
                'num_points': len(est_pos)
            }
            coord_data.append(est_stats)

            # Baseline trajectory statistics (convert to NED coordinates for consistency)
            if baseline_data and 'position' in baseline_data:
                base_pos = baseline_data['position']

                # COORDINATE SYSTEM FIX: Convert baseline from UTM [Easting, Northing, Down] to NED
                base_pos_ned = np.column_stack([
                    base_pos[:, 1],  # Northing (y) -> North
                    base_pos[:, 0],  # Easting (x) -> East
                    base_pos[:, 2]   # Down (z) -> Down
                ])

                base_stats = {
                    'frame_type': 'baseline',
                    'north_min': np.min(base_pos_ned[:, 0]),
                    'north_max': np.max(base_pos_ned[:, 0]),
                    'north_mean': np.mean(base_pos_ned[:, 0]),
                    'east_min': np.min(base_pos_ned[:, 1]),
                    'east_max': np.max(base_pos_ned[:, 1]),
                    'east_mean': np.mean(base_pos_ned[:, 1]),
                    'down_min': np.min(base_pos_ned[:, 2]),
                    'down_max': np.max(base_pos_ned[:, 2]),
                    'down_mean': np.mean(base_pos_ned[:, 2]),
                    'num_points': len(base_pos_ned)
                }
                coord_data.append(base_stats)

                # Calculate alignment transformation
                est_center = np.mean(est_pos, axis=0)
                base_center = np.mean(base_pos, axis=0)
                translation = base_center - est_center

                transform_stats = {
                    'frame_type': 'transformation',
                    'north_min': translation[0],
                    'north_max': translation[0],
                    'north_mean': translation[0],
                    'east_min': translation[1],
                    'east_max': translation[1],
                    'east_mean': translation[1],
                    'down_min': translation[2],
                    'down_max': translation[2],
                    'down_mean': translation[2],
                    'num_points': 1
                }
                coord_data.append(transform_stats)

            # Create DataFrame and save
            coord_df = pd.DataFrame(coord_data)
            coord_file = output_dir / "coordinate_frames.csv"
            coord_df.to_csv(coord_file, index=False)
            logger.info(f"Coordinate frame data exported to: {coord_file}")

        except Exception as e:
            logger.error(f"Error exporting coordinate frame data: {e}")
            raise
