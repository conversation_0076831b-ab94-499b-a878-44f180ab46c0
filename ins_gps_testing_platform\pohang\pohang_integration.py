"""
Integration script for Pohang dataset processing with LiDAR georeferencing
Combines INS/GPS fusion, LiDAR georeferencing, and trajectory comparison
"""

import numpy as np
import os
from pathlib import Path
import logging
from typing import Dict, Optional
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend for threading
import matplotlib.pyplot as plt

# Import your existing modules
import sys
from pathlib import Path
# Add parent directory to path for absolute imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from pohang.pohang_analizor import PohangDatasetLoader, PohangDatasetConfig
from fusion.fusion_engine import FusionEngine
from config.configuration_manager import SensorConfig
from preprocessing.marine_imu_preprocessor import PreprocessingConfig

# Import the georeferencing and trajectory comparison tools
from pohang.lidar_georeferencing import LidarGeoreferencer
from pohang.trajectory_comparison import TrajectoryComparator

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PohangLidarProcessor:
    """
    Complete pipeline for processing Pohang dataset with LiDAR georeferencing
    """
    
    def __init__(self, dataset_path: str, preprocess_config: PreprocessingConfig):
        """
        Initialize the processor

        Args:
            dataset_path: Path to Pohang dataset root directory
            preprocess_config: Configuration object for IMU preprocessing
        """
        self.dataset_path = Path(dataset_path)
        self.preprocess_config = preprocess_config

        # Initialize dataset loader
        self.dataset_loader = PohangDatasetLoader(dataset_path)

        # Get LGF reference location from dataset loader (extracted from baseline or GPS)
        self.reference_location = self.dataset_loader.get_lgf_reference_location()
        logger.info(f"Using LGF reference location: {self.reference_location}")
        
        # Initialize LiDAR georeferencer
        extrinsics_path = self.dataset_path / "calibration" / "extrinsics.json"
        self.georeferencer = LidarGeoreferencer(
            str(extrinsics_path),
            self.reference_location
        )
        
        # Initialize trajectory comparator
        self.comparator = TrajectoryComparator()
        
        # Storage for results
        self.fusion_results = None
        self.baseline_data = None
        
    def run_ins_gps_fusion(self) -> Dict:
        """
        Run INS/GPS fusion on the dataset
        
        Returns:
            Fusion results dictionary
        """
        logger.info("Running INS/GPS fusion...")
        
        # Load and prepare data with original timestamps (baseline is never used as ground truth for fusion)
        simulation_data = self.dataset_loader.prepare_for_fusion(preprocess_config=self.preprocess_config)

        # Always load baseline for evaluation (not for fusion)
        if self.dataset_loader.baseline_data is not None:
            # Keep baseline in original timestamps for now - will synchronize only when needed for comparison
            self.baseline_data = {
                'time': self.dataset_loader.baseline_data['time'],
                'position': self.dataset_loader.baseline_data['position'],
                'quaternion': self.dataset_loader.baseline_data['quaternion']
            }
            logger.info("Baseline data loaded with original timestamps for evaluation")
        
        # Run fusion
        fusion_engine = FusionEngine()
        sensor_config = simulation_data.configuration['sensors']
        results = fusion_engine.run_fusion(
            simulation_data,
            sensor_config,
            lgf_reference=self.reference_location
        )
        
        # Convert results to dictionary format
        self.fusion_results = {
            'time': results.time,
            'position': results.position,
            'velocity': results.velocity,
            'acceleration': results.acceleration,
            'orientation': results.orientation,
            'angular_velocity': results.angular_velocity,
            'sensor_biases': results.sensor_biases,
            'covariance_trace': results.covariance_trace
        }
        
        logger.info(f"Fusion completed. Processed {len(results.time)} samples.")
        
        return self.fusion_results
    
    def load_lidar_data(self, lidar_file: str, max_points: Optional[int] = None) -> np.ndarray:
        """
        Load LiDAR point cloud data
        
        Args:
            lidar_file: Path to LiDAR data file
            max_points: Maximum number of points to load (for memory management)
            
        Returns:
            Point cloud array [N x 3] or [N x 4] (if intensity included)
        """
        # This is a placeholder - you would implement actual LiDAR file loading here
        # Common formats: .pcd, .las, .laz, .bin (KITTI format), etc.
        
        logger.info(f"Loading LiDAR data from {lidar_file}")
        
        # Example for binary format (like KITTI)
        if lidar_file.endswith('.bin'):
            points = np.fromfile(lidar_file, dtype=np.float32)
            points = points.reshape(-1, 4)  # x, y, z, intensity
            
            if max_points is not None and len(points) > max_points:
                # Random sampling to reduce points
                indices = np.random.choice(len(points), max_points, replace=False)
                points = points[indices]
        else:
            # For demo, create synthetic data
            logger.warning(f"Unknown LiDAR format. Creating synthetic data for demo.")
            num_points = max_points or 10000
            # Simulate LiDAR scan pattern
            angles = np.random.uniform(-np.pi, np.pi, num_points)
            ranges = np.random.uniform(0.5, 50.0, num_points)
            heights = np.random.uniform(-2, 2, num_points)
            
            points = np.column_stack([
                ranges * np.cos(angles),
                ranges * np.sin(angles),
                heights
            ])
        
        logger.info(f"Loaded {len(points)} points")
        return points[:, :3]  # Return only xyz
    
    def georeference_lidar_scan(self, 
                               lidar_points: np.ndarray,
                               timestamp: float,
                               sensor_name: str = 'lidar_front') -> np.ndarray:
        """
        Georeference a single LiDAR scan using INS/GPS results at given timestamp
        
        Args:
            lidar_points: Raw LiDAR points [N x 3]
            timestamp: Timestamp of the scan
            sensor_name: Name of the LiDAR sensor
            
        Returns:
            Georeferenced points in NED frame
        """
        if self.fusion_results is None:
            raise ValueError("Must run INS/GPS fusion first")
        
        # Find closest INS/GPS result
        time_idx = np.argmin(np.abs(self.fusion_results['time'] - timestamp))
        
        # Get INS state at this time
        ins_position = self.fusion_results['position'][time_idx]
        ins_orientation = self.fusion_results['orientation'][time_idx]
        
        # Georeference the points
        georef_points = self.georeferencer.georeference_point_cloud(
            lidar_points,
            ins_orientation,
            ins_position,
            sensor_name=sensor_name,
            output_frame='ned'
        )
        
        return georef_points
    
    def process_lidar_sequence(self,
                              lidar_files: list,
                              timestamps: np.ndarray,
                              sensor_name: str = 'lidar_front',
                              save_georeferenced: bool = False,
                              output_dir: Optional[str] = None) -> Dict:
        """
        Process a sequence of LiDAR scans
        
        Args:
            lidar_files: List of LiDAR file paths
            timestamps: Corresponding timestamps
            sensor_name: Name of the LiDAR sensor
            save_georeferenced: Whether to save georeferenced point clouds
            output_dir: Directory to save georeferenced data
            
        Returns:
            Dictionary with processing results
        """
        if len(lidar_files) != len(timestamps):
            raise ValueError("Number of files must match number of timestamps")
        
        logger.info(f"Processing {len(lidar_files)} LiDAR scans...")
        
        results = {
            'timestamps': timestamps,
            'sensor_positions': [],
            'num_points': [],
            'georef_bounds': []
        }
        
        if save_georeferenced and output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        for i, (lidar_file, timestamp) in enumerate(zip(lidar_files, timestamps)):
            # Load LiDAR data
            points = self.load_lidar_data(lidar_file, max_points=50000)
            results['num_points'].append(len(points))
            
            # Georeference
            georef_points = self.georeference_lidar_scan(
                points, timestamp, sensor_name
            )
            
            # Compute bounds
            bounds = {
                'min': georef_points.min(axis=0),
                'max': georef_points.max(axis=0)
            }
            results['georef_bounds'].append(bounds)
            
            # Get sensor position
            time_idx = np.argmin(np.abs(self.fusion_results['time'] - timestamp))
            sensor_pos = self.georeferencer.georeference_trajectory(
                self.fusion_results['position'][time_idx:time_idx+1],
                self.fusion_results['orientation'][time_idx:time_idx+1],
                sensor_name
            )[0]
            results['sensor_positions'].append(sensor_pos)
            
            # Save if requested
            if save_georeferenced and output_dir:
                output_file = os.path.join(output_dir, f"georef_{i:06d}.npy")
                np.save(output_file, georef_points)
            
            if (i + 1) % 10 == 0:
                logger.info(f"Processed {i + 1}/{len(lidar_files)} scans")
        
        results['sensor_positions'] = np.array(results['sensor_positions'])
        
        logger.info("LiDAR processing completed")
        return results
    
    def compare_with_baseline(self) -> Dict:
        """
        Compare fusion results with baseline trajectory
        
        Returns:
            Error metrics dictionary
        """
        if self.fusion_results is None:
            raise ValueError("Must run INS/GPS fusion first")
        
        if self.baseline_data is None:
            # Try to load baseline data (uses cached loading)
            try:
                self.dataset_loader.load_baseline_data()
                # Use synchronized data only for comparison purposes
                synced_data = self.dataset_loader.get_synchronized_data_for_comparison()
                if synced_data['baseline'] is not None:
                    self.baseline_data = {
                        'time': synced_data['time'],
                        'position': synced_data['baseline']['position'],
                        'quaternion': synced_data['baseline']['quaternion']
                    }
                    logger.info("Baseline data synchronized for comparison purposes")
            except Exception as e:
                logger.error(f"Could not load baseline data for comparison: {e}")
                return None
        
        # Compute error metrics
        metrics = self.comparator.compute_errors(
            self.fusion_results,
            self.baseline_data
        )
        
        # Print summary
        self.comparator.print_summary()
        
        return metrics
    
    def visualize_results(self,
                         show_lidar_trajectory: bool = True,
                         save_plots: bool = True,
                         output_dir: str = "../exports/pohang_results"):
        """
        Comprehensive visualization of fusion results and comparisons
        """
        if save_plots:
            os.makedirs(output_dir, exist_ok=True)
            logger.info(f"📁 Results will be saved to: {os.path.abspath(output_dir)}")

        # 1. Plot individual trajectories for diagnosis
        if save_plots:
            self._plot_individual_trajectories(output_dir)

        # 2. Plot comprehensive trajectory comparison if baseline available
        if self.baseline_data is not None:
            logger.info("Creating comprehensive trajectory comparison...")
            self._plot_comprehensive_comparison(output_dir if save_plots else None)

            # 3. Generate detailed error analysis
            self._plot_detailed_error_analysis(output_dir if save_plots else None)

            # 4. Create coordinate frame alignment visualization
            self._plot_coordinate_frame_alignment(output_dir if save_plots else None)
        
        # Plot sensor trajectories
        if show_lidar_trajectory:
            fig = plt.figure(figsize=(12, 8))
            ax = fig.add_subplot(111)
            
            # Vehicle trajectory
            ax.plot(self.fusion_results['position'][:, 1],
                   self.fusion_results['position'][:, 0],
                   'b-', label='Vehicle (Body)', linewidth=2)
            
            # LiDAR sensor trajectories
            for sensor_name in ['lidar_front', 'lidar_port', 'lidar_starboard']:
                if sensor_name in self.georeferencer.extrinsics:
                    sensor_traj = self.georeferencer.georeference_trajectory(
                        self.fusion_results['position'],
                        self.fusion_results['orientation'],
                        sensor_name
                    )
                    ax.plot(sensor_traj[:, 1], sensor_traj[:, 0],
                           '--', label=sensor_name, linewidth=1.5)
            
            ax.set_xlabel('East (m)')
            ax.set_ylabel('North (m)')
            ax.set_title('Vehicle and Sensor Trajectories')
            ax.legend()
            ax.grid(True)
            ax.axis('equal')
            
            if save_plots:
                trajectory_file = os.path.join(output_dir, "sensor_trajectories.png")
                plt.savefig(trajectory_file, dpi=300, bbox_inches='tight')
                logger.info(f"📊 Sensor trajectories saved to: {os.path.abspath(trajectory_file)}")
            plt.close()  # Close instead of show for background thread
        
        # Plot error statistics over time
        if hasattr(self.comparator, 'metrics'):
            fig, axes = plt.subplots(2, 1, figsize=(12, 8))
            
            time = self.comparator.metrics['time_series']['time']
            errors = self.comparator.metrics['time_series']['position_errors']
            
            # Position errors
            axes[0].plot(time, errors[:, 0], 'r-', label='North')
            axes[0].plot(time, errors[:, 1], 'g-', label='East')
            axes[0].plot(time, errors[:, 2], 'b-', label='Down')
            axes[0].set_ylabel('Position Error (m)')
            axes[0].set_title('Position Error Components')
            axes[0].legend()
            axes[0].grid(True)
            
            # 3D error
            axes[1].plot(time, self.comparator.metrics['time_series']['position_3d_errors'], 'k-')
            axes[1].set_xlabel('Time (s)')
            axes[1].set_ylabel('3D Position Error (m)')
            axes[1].set_title('Total Position Error')
            axes[1].grid(True)
            
            plt.tight_layout()
            if save_plots:
                error_file = os.path.join(output_dir, "error_analysis.png")
                plt.savefig(error_file, dpi=300, bbox_inches='tight')
                logger.info(f"📈 Error analysis saved to: {os.path.abspath(error_file)}")
            plt.close()  # Close instead of show for background thread

        logger.info("Visualization completed")

    def _plot_individual_trajectories(self, output_dir: str):
        """Plot estimated and ground truth trajectories separately for diagnosis"""

        # Plot 1: Estimated trajectory only
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        # 3D view of estimated trajectory
        ax1 = fig.add_subplot(121, projection='3d')
        if self.fusion_results is not None:
            pos = self.fusion_results['position']
            ax1.plot(pos[:, 0], pos[:, 1], pos[:, 2], 'b-', linewidth=2, label='Estimated')
            ax1.set_xlabel('North (m)')
            ax1.set_ylabel('East (m)')
            ax1.set_zlabel('Down (m)')
            ax1.set_title('Estimated Trajectory (3D) - Original Frame')
            ax1.legend()

            # Add coordinate info
            ax1.text2D(0.02, 0.98, f'Range N: [{pos[:, 0].min():.1f}, {pos[:, 0].max():.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')
            ax1.text2D(0.02, 0.94, f'Range E: [{pos[:, 1].min():.1f}, {pos[:, 1].max():.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')
            ax1.text2D(0.02, 0.90, f'Range D: [{pos[:, 2].min():.1f}, {pos[:, 2].max():.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')
            ax1.text2D(0.02, 0.86, f'Center: [{np.mean(pos, axis=0)[0]:.1f}, {np.mean(pos, axis=0)[1]:.1f}, {np.mean(pos, axis=0)[2]:.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')

        # 2D top view of estimated trajectory
        ax2 = fig.add_subplot(122)
        if self.fusion_results is not None:
            pos = self.fusion_results['position']
            ax2.plot(pos[:, 1], pos[:, 0], 'b-', linewidth=2, label='Estimated')
            ax2.set_xlabel('East (m)')
            ax2.set_ylabel('North (m)')
            ax2.set_title('Estimated Trajectory (Top View) - Original Frame')
            ax2.grid(True)
            ax2.legend()
            ax2.axis('equal')

        plt.tight_layout()
        estimated_file = os.path.join(output_dir, "estimated_trajectory_only.png")
        plt.savefig(estimated_file, dpi=300, bbox_inches='tight')
        logger.info(f"📊 Estimated trajectory saved to: {os.path.abspath(estimated_file)}")
        plt.close()

        # Plot 2: Ground truth trajectory only
        if self.baseline_data is not None:
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # 3D view of ground truth
            ax1 = fig.add_subplot(121, projection='3d')
            baseline_pos = self.baseline_data['position']

            ax1.plot(baseline_pos[:, 0], baseline_pos[:, 1], baseline_pos[:, 2], 'r--', linewidth=2, label='Ground Truth')
            ax1.set_xlabel('North (m)')
            ax1.set_ylabel('East (m)')
            ax1.set_zlabel('Down (m)')
            ax1.set_title('Ground Truth Trajectory (3D)')
            ax1.legend()

            # Add coordinate info
            ax1.text2D(0.02, 0.98, f'Range N: [{baseline_pos[:, 0].min():.1f}, {baseline_pos[:, 0].max():.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')
            ax1.text2D(0.02, 0.94, f'Range E: [{baseline_pos[:, 1].min():.1f}, {baseline_pos[:, 1].max():.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')
            ax1.text2D(0.02, 0.90, f'Range D: [{baseline_pos[:, 2].min():.1f}, {baseline_pos[:, 2].max():.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')
            ax1.text2D(0.02, 0.86, f'Center: [{np.mean(baseline_pos, axis=0)[0]:.1f}, {np.mean(baseline_pos, axis=0)[1]:.1f}, {np.mean(baseline_pos, axis=0)[2]:.1f}]',
                      transform=ax1.transAxes, fontsize=8, verticalalignment='top')

            # 2D top view of ground truth
            ax2 = fig.add_subplot(122)
            ax2.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r--', linewidth=2, label='Ground Truth')
            ax2.set_xlabel('East (m)')
            ax2.set_ylabel('North (m)')
            ax2.set_title('Ground Truth Trajectory (Top View)')
            ax2.grid(True)
            ax2.legend()
            ax2.axis('equal')

            plt.tight_layout()
            baseline_file = os.path.join(output_dir, "ground_truth_trajectory_only.png")
            plt.savefig(baseline_file, dpi=300, bbox_inches='tight')
            logger.info(f"📊 Ground truth trajectory saved to: {os.path.abspath(baseline_file)}")
            plt.close()

    def _plot_comprehensive_comparison(self, output_dir: Optional[str] = None):
        """
        Create comprehensive trajectory comparison in the same coordinate frame
        """
        if self.fusion_results is None or self.baseline_data is None:
            logger.warning("Cannot create comparison - missing data")
            return

        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D

        # Convert estimated trajectory to baseline coordinate frame
        est_pos_original = self.fusion_results['position']
        baseline_pos = self.baseline_data['position']

        # Simple coordinate system alignment: translate estimated trajectory to baseline frame
        est_center = np.mean(est_pos_original, axis=0)
        baseline_center = np.mean(baseline_pos, axis=0)

        # Align estimated trajectory with baseline coordinate system
        est_pos = est_pos_original - est_center + baseline_center

        logger.info(f"Coordinate system alignment:")
        logger.info(f"  Estimated center: [{est_center[0]:.1f}, {est_center[1]:.1f}, {est_center[2]:.1f}]")
        logger.info(f"  Baseline center: [{baseline_center[0]:.1f}, {baseline_center[1]:.1f}, {baseline_center[2]:.1f}]")
        logger.info(f"  Translation applied: [{baseline_center[0]-est_center[0]:.1f}, {baseline_center[1]-est_center[1]:.1f}, {baseline_center[2]-est_center[2]:.1f}]")

        # Create comprehensive comparison figure
        fig = plt.figure(figsize=(20, 12))
        fig.suptitle('Comprehensive Trajectory Comparison: Estimated vs Ground Truth Baseline', fontsize=16)

        # 1. 3D trajectory comparison
        ax1 = fig.add_subplot(231, projection='3d')

        ax1.plot(est_pos[:, 1], est_pos[:, 0], -est_pos[:, 2], 'b-', linewidth=2, label='Estimated', alpha=0.8)
        ax1.plot(baseline_pos[:, 1], baseline_pos[:, 0], -baseline_pos[:, 2], 'r--', linewidth=2, label='Ground Truth', alpha=0.8)

        # Mark start and end points
        ax1.scatter([est_pos[0, 1]], [est_pos[0, 0]], [-est_pos[0, 2]], c='blue', s=100, marker='o', label='Est Start')
        ax1.scatter([est_pos[-1, 1]], [est_pos[-1, 0]], [-est_pos[-1, 2]], c='blue', s=100, marker='s', label='Est End')
        ax1.scatter([baseline_pos[0, 1]], [baseline_pos[0, 0]], [-baseline_pos[0, 2]], c='red', s=100, marker='o', label='GT Start')
        ax1.scatter([baseline_pos[-1, 1]], [baseline_pos[-1, 0]], [-baseline_pos[-1, 2]], c='red', s=100, marker='s', label='GT End')

        ax1.set_xlabel('East (m)')
        ax1.set_ylabel('North (m)')
        ax1.set_zlabel('Up (m)')
        ax1.set_title('3D Trajectory Comparison')
        ax1.legend(fontsize=8)

        # 2. 2D top view with equal aspect ratio
        ax2 = fig.add_subplot(232)
        ax2.plot(est_pos[:, 1], est_pos[:, 0], 'b-', linewidth=2, label='Estimated', alpha=0.8)
        ax2.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r--', linewidth=2, label='Ground Truth', alpha=0.8)
        ax2.scatter([est_pos[0, 1]], [est_pos[0, 0]], c='blue', s=50, marker='o')
        ax2.scatter([est_pos[-1, 1]], [est_pos[-1, 0]], c='blue', s=50, marker='s')
        ax2.scatter([baseline_pos[0, 1]], [baseline_pos[0, 0]], c='red', s=50, marker='o')
        ax2.scatter([baseline_pos[-1, 1]], [baseline_pos[-1, 0]], c='red', s=50, marker='s')
        ax2.set_xlabel('East (m)')
        ax2.set_ylabel('North (m)')
        ax2.set_title('2D Trajectory (Top View)')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.axis('equal')

        # Add coordinate frame info
        est_range_n = [est_pos[:, 0].min(), est_pos[:, 0].max()]
        est_range_e = [est_pos[:, 1].min(), est_pos[:, 1].max()]
        baseline_range_n = [baseline_pos[:, 0].min(), baseline_pos[:, 0].max()]
        baseline_range_e = [baseline_pos[:, 1].min(), baseline_pos[:, 1].max()]

        info_text = f"Estimated Range - N: [{est_range_n[0]:.1f}, {est_range_n[1]:.1f}], E: [{est_range_e[0]:.1f}, {est_range_e[1]:.1f}]\n"
        info_text += f"Baseline Range - N: [{baseline_range_n[0]:.1f}, {baseline_range_n[1]:.1f}], E: [{baseline_range_e[0]:.1f}, {baseline_range_e[1]:.1f}]"
        ax2.text(0.02, 0.98, info_text, transform=ax2.transAxes, fontsize=8,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()

        if output_dir:
            comparison_file = os.path.join(output_dir, "comprehensive_trajectory_comparison.png")
            plt.savefig(comparison_file, dpi=300, bbox_inches='tight')
            logger.info(f"📊 Comprehensive comparison saved to: {os.path.abspath(comparison_file)}")

        plt.close()

    def _plot_detailed_error_analysis(self, output_dir: Optional[str] = None):
        """
        Create detailed error analysis plots
        """
        if self.fusion_results is None or self.baseline_data is None:
            logger.warning("Cannot create error analysis - missing data")
            return

        import matplotlib.pyplot as plt
        import numpy as np
        from scipy.interpolate import interp1d

        # Use spatial synchronization results from TrajectoryComparator if available
        if hasattr(self.comparator, 'metrics') and 'time_series' in self.comparator.metrics:
            logger.info("Using spatial synchronization results for error analysis")

            # Get spatially synchronized data from comparator
            est_time_common = self.comparator.metrics['time_series']['time']
            pos_errors = self.comparator.metrics['time_series']['position_errors']

            # Calculate distance errors from spatially synchronized data
            horizontal_errors = np.sqrt(pos_errors[:, 0]**2 + pos_errors[:, 1]**2)
            vertical_errors = np.abs(pos_errors[:, 2])

        else:
            logger.warning("No spatial synchronization results available, falling back to time-based interpolation")

            # Fallback to original time-based method
            est_pos_original = self.fusion_results['position']
            est_time = self.fusion_results['time']
            baseline_pos = self.baseline_data['position']
            baseline_time = self.baseline_data['time']

            # Align estimated trajectory with baseline coordinate system
            est_center = np.mean(est_pos_original, axis=0)

            baseline_center = np.mean(baseline_pos, axis=0)
            est_pos = est_pos_original - est_center + baseline_center

            # Interpolate baseline to match estimated trajectory timestamps
            if len(baseline_time) > 1 and len(est_time) > 1:
                # Find common time range
                t_start = max(est_time[0], baseline_time[0])
                t_end = min(est_time[-1], baseline_time[-1])

                # Filter to common time range
                est_mask = (est_time >= t_start) & (est_time <= t_end)
                baseline_mask = (baseline_time >= t_start) & (baseline_time <= t_end)

                est_time_common = est_time[est_mask]
                est_pos_common = est_pos[est_mask]

                # Interpolate baseline to estimated timestamps
                baseline_interp_funcs = [
                    interp1d(baseline_time[baseline_mask], baseline_pos[baseline_mask, i],
                            kind='linear', bounds_error=False, fill_value='extrapolate')
                    for i in range(3)
                ]

                baseline_pos_interp = np.column_stack([
                    func(est_time_common) for func in baseline_interp_funcs
                ])

                # Calculate position errors
                pos_errors = est_pos_common - baseline_pos_interp

                # Calculate distance errors
                horizontal_errors = np.sqrt(pos_errors[:, 0]**2 + pos_errors[:, 1]**2)
                vertical_errors = np.abs(pos_errors[:, 2])
            else:
                logger.error("Insufficient data for error analysis")
                return

        # Calculate total 3D errors (common for both spatial and time-based methods)
        total_errors = np.sqrt(np.sum(pos_errors**2, axis=1))

        # Create error analysis figure
        fig = plt.figure(figsize=(16, 12))
        fig.suptitle('Detailed Error Analysis: Estimated vs Ground Truth', fontsize=16)

        # 1. Position error components vs time
        ax1 = fig.add_subplot(231)
        ax1.plot(est_time_common, pos_errors[:, 0], 'r-', linewidth=1.5, label='North Error', alpha=0.8)
        ax1.plot(est_time_common, pos_errors[:, 1], 'g-', linewidth=1.5, label='East Error', alpha=0.8)
        ax1.plot(est_time_common, pos_errors[:, 2], 'b-', linewidth=1.5, label='Down Error', alpha=0.8)
        ax1.set_xlabel('Time (s)')
        ax1.set_ylabel('Position Error (m)')
        ax1.set_title('Position Error Components')
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # 2. Horizontal and vertical errors
        ax2 = fig.add_subplot(232)
        ax2.plot(est_time_common, horizontal_errors, 'r-', linewidth=2, label='Horizontal Error', alpha=0.8)
        ax2.plot(est_time_common, vertical_errors, 'b-', linewidth=2, label='Vertical Error', alpha=0.8)
        ax2.set_xlabel('Time (s)')
        ax2.set_ylabel('Error (m)')
        ax2.set_title('Horizontal and Vertical Errors')
        ax2.grid(True, alpha=0.3)
        ax2.legend()

        # 3. Total 3D error
        ax3 = fig.add_subplot(233)
        ax3.plot(est_time_common, total_errors, 'k-', linewidth=2, alpha=0.8)
        ax3.set_xlabel('Time (s)')
        ax3.set_ylabel('3D Error (m)')
        ax3.set_title('Total 3D Position Error')
        ax3.grid(True, alpha=0.3)

        # 4. Error statistics
        ax4 = fig.add_subplot(234)
        ax4.axis('off')

        # Calculate statistics
        stats_text = "Error Statistics:\n\n"
        stats_text += f"Horizontal Error (m):\n"
        stats_text += f"  Mean: {np.mean(horizontal_errors):.3f}\n"
        stats_text += f"  RMS: {np.sqrt(np.mean(horizontal_errors**2)):.3f}\n"
        stats_text += f"  Max: {np.max(horizontal_errors):.3f}\n"
        stats_text += f"  Std: {np.std(horizontal_errors):.3f}\n\n"

        stats_text += f"Vertical Error (m):\n"
        stats_text += f"  Mean: {np.mean(vertical_errors):.3f}\n"
        stats_text += f"  RMS: {np.sqrt(np.mean(vertical_errors**2)):.3f}\n"
        stats_text += f"  Max: {np.max(vertical_errors):.3f}\n"
        stats_text += f"  Std: {np.std(vertical_errors):.3f}\n\n"

        stats_text += f"3D Error (m):\n"
        stats_text += f"  Mean: {np.mean(total_errors):.3f}\n"
        stats_text += f"  RMS: {np.sqrt(np.mean(total_errors**2)):.3f}\n"
        stats_text += f"  Max: {np.max(total_errors):.3f}\n"
        stats_text += f"  Std: {np.std(total_errors):.3f}"

        ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))

        # 5. Error histogram
        ax5 = fig.add_subplot(235)
        ax5.hist(horizontal_errors, bins=30, alpha=0.7, color='red', label='Horizontal', density=True)
        ax5.hist(vertical_errors, bins=30, alpha=0.7, color='blue', label='Vertical', density=True)
        ax5.set_xlabel('Error (m)')
        ax5.set_ylabel('Density')
        ax5.set_title('Error Distribution')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. Trajectory error visualization
        ax6 = fig.add_subplot(236)

        # For spatial synchronization, we need to get the synchronized positions
        if hasattr(self.comparator, 'metrics') and 'time_series' in self.comparator.metrics:
            # Use spatially synchronized estimated positions
            if hasattr(self.comparator, 'spatial_sync_indices') and self.comparator.spatial_sync_indices is not None:
                est_indices = self.comparator.spatial_sync_indices['estimated_indices']
                base_indices = self.comparator.spatial_sync_indices['baseline_indices']
                est_pos_sync = self.fusion_results['position'][est_indices]
                baseline_pos_sync = self.baseline_data['position'][base_indices]
            else:
                # Fallback to simple truncation
                min_len = min(len(self.fusion_results['position']), len(self.baseline_data['position']))
                est_pos_sync = self.fusion_results['position'][:min_len]
                baseline_pos_sync = self.baseline_data['position'][:min_len]
        else:
            # Use the interpolated data from fallback method
            est_pos_sync = est_pos_common
            baseline_pos_sync = baseline_pos_interp

        scatter = ax6.scatter(est_pos_sync[:, 1], est_pos_sync[:, 0],
                            c=total_errors, cmap='viridis', s=20, alpha=0.8)
        ax6.plot(baseline_pos_sync[:, 1], baseline_pos_sync[:, 0], 'r--',
                linewidth=2, label='Ground Truth', alpha=0.8)
        ax6.set_xlabel('East (m)')
        ax6.set_ylabel('North (m)')
        ax6.set_title('Trajectory with Error Magnitude')
        ax6.grid(True, alpha=0.3)
        ax6.legend()
        ax6.axis('equal')

        # Add colorbar
        cbar = plt.colorbar(scatter, ax=ax6)
        cbar.set_label('3D Error (m)')

        plt.tight_layout()

        if output_dir:
            error_file = os.path.join(output_dir, "detailed_error_analysis.png")
            plt.savefig(error_file, dpi=300, bbox_inches='tight')
            logger.info(f"📊 Error analysis saved to: {os.path.abspath(error_file)}")

        plt.close()

    def _plot_coordinate_frame_alignment(self, output_dir: Optional[str] = None):
        """
        Create coordinate frame alignment visualization
        """
        if self.fusion_results is None or self.baseline_data is None:
            logger.warning("Cannot create coordinate frame visualization - missing data")
            return

        import matplotlib.pyplot as plt

        # Get trajectory data and align coordinate systems
        est_pos_original = self.fusion_results['position']
        baseline_pos = self.baseline_data['position']

        # Align estimated trajectory with baseline coordinate system
        est_center = np.mean(est_pos_original, axis=0)
        baseline_center = np.mean(baseline_pos, axis=0)
        est_pos = est_pos_original - est_center + baseline_center

        # Create coordinate frame alignment figure
        fig = plt.figure(figsize=(16, 10))
        fig.suptitle('Coordinate Frame Alignment Verification', fontsize=16)

        # 1. Overlay trajectories with coordinate system info
        ax1 = fig.add_subplot(221)

        ax1.plot(est_pos[:, 1], est_pos[:, 0], 'b-', linewidth=2, label='Estimated Trajectory', alpha=0.8)
        ax1.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r--', linewidth=2, label='Ground Truth Baseline', alpha=0.8)

        # Mark origins and directions
        ax1.scatter([0], [0], c='black', s=100, marker='+', linewidth=3, label='LGF Origin')
        ax1.arrow(0, 0, 50, 0, head_width=10, head_length=10, fc='red', ec='red', alpha=0.7)
        ax1.arrow(0, 0, 0, 50, head_width=10, head_length=10, fc='green', ec='green', alpha=0.7)
        ax1.text(55, 0, 'East (+Y)', fontsize=10, color='red')
        ax1.text(0, 55, 'North (+X)', fontsize=10, color='green')

        ax1.set_xlabel('East (m)')
        ax1.set_ylabel('North (m)')
        ax1.set_title('Trajectory Overlay in Local Geodetic Frame')
        ax1.grid(True, alpha=0.3)
        ax1.legend()
        ax1.axis('equal')

        # 2. Coordinate system information
        ax2 = fig.add_subplot(222)
        ax2.axis('off')

        coord_info = "Coordinate System Details:\n\n"
        coord_info += "Baseline Coordinate Frame:\n"
        coord_info += "• Both trajectories aligned to baseline frame\n"
        coord_info += "• Simple translation applied to estimated trajectory\n\n"
        coord_info += "Coordinate Axes:\n"
        coord_info += "• X-axis: North (positive northward)\n"
        coord_info += "• Y-axis: East (positive eastward)\n"
        coord_info += "• Z-axis: Down (positive downward)\n\n"
        coord_info += "Alignment Method:\n"
        coord_info += "• Estimated trajectory translated to baseline center\n"
        coord_info += "• Both trajectories now in same coordinate system\n\n"
        coord_info += "Data Sources:\n"
        coord_info += "• Estimated: GPS/IMU fusion result (aligned)\n"
        coord_info += "• Ground Truth: Baseline trajectory (reference)"

        ax2.text(0.05, 0.95, coord_info, transform=ax2.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

        # 3. Range comparison
        ax3 = fig.add_subplot(223)

        # Calculate ranges
        est_range_n = [est_pos[:, 0].min(), est_pos[:, 0].max()]
        est_range_e = [est_pos[:, 1].min(), est_pos[:, 1].max()]
        est_range_d = [est_pos[:, 2].min(), est_pos[:, 2].max()]

        baseline_range_n = [baseline_pos[:, 0].min(), baseline_pos[:, 0].max()]
        baseline_range_e = [baseline_pos[:, 1].min(), baseline_pos[:, 1].max()]
        baseline_range_d = [baseline_pos[:, 2].min(), baseline_pos[:, 2].max()]

        # Plot range comparison
        categories = ['North', 'East', 'Down']
        est_ranges = [est_range_n[1] - est_range_n[0], est_range_e[1] - est_range_e[0], est_range_d[1] - est_range_d[0]]
        baseline_ranges = [baseline_range_n[1] - baseline_range_n[0], baseline_range_e[1] - baseline_range_e[0], baseline_range_d[1] - baseline_range_d[0]]

        x = np.arange(len(categories))
        width = 0.35

        ax3.bar(x - width/2, est_ranges, width, label='Estimated', alpha=0.8, color='blue')
        ax3.bar(x + width/2, baseline_ranges, width, label='Ground Truth', alpha=0.8, color='red')

        ax3.set_xlabel('Coordinate Axis')
        ax3.set_ylabel('Range (m)')
        ax3.set_title('Coordinate Range Comparison')
        ax3.set_xticks(x)
        ax3.set_xticklabels(categories)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. Alignment verification
        ax4 = fig.add_subplot(224)
        ax4.axis('off')

        # Calculate alignment metrics
        est_center = np.mean(est_pos, axis=0)
        baseline_center = np.mean(baseline_pos, axis=0)
        center_offset = est_center - baseline_center

        alignment_info = "Alignment Verification:\n\n"
        alignment_info += f"Trajectory Centers:\n"
        alignment_info += f"• Estimated: [{est_center[0]:.1f}, {est_center[1]:.1f}, {est_center[2]:.1f}]\n"
        alignment_info += f"• Baseline: [{baseline_center[0]:.1f}, {baseline_center[1]:.1f}, {baseline_center[2]:.1f}]\n"
        alignment_info += f"• Offset: [{center_offset[0]:.1f}, {center_offset[1]:.1f}, {center_offset[2]:.1f}]\n\n"

        alignment_info += f"Range Comparison:\n"
        alignment_info += f"• North: Est={est_ranges[0]:.1f}m, GT={baseline_ranges[0]:.1f}m\n"
        alignment_info += f"• East: Est={est_ranges[1]:.1f}m, GT={baseline_ranges[1]:.1f}m\n"
        alignment_info += f"• Down: Est={est_ranges[2]:.1f}m, GT={baseline_ranges[2]:.1f}m\n\n"

        # Check if coordinate systems are aligned
        offset_magnitude = np.linalg.norm(center_offset)
        if offset_magnitude < 100:  # Within 100m
            alignment_info += "✓ Coordinate systems appear aligned\n"
            alignment_info += f"  Center offset: {offset_magnitude:.1f}m"
        else:
            alignment_info += "⚠ Possible coordinate system mismatch\n"
            alignment_info += f"  Center offset: {offset_magnitude:.1f}m"

        ax4.text(0.05, 0.95, alignment_info, transform=ax4.transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))

        plt.tight_layout()

        if output_dir:
            alignment_file = os.path.join(output_dir, "coordinate_frame_alignment.png")
            plt.savefig(alignment_file, dpi=300, bbox_inches='tight')
            logger.info(f"📊 Coordinate frame alignment saved to: {os.path.abspath(alignment_file)}")

        plt.close()


# Main processing function
def process_pohang_dataset(dataset_path: str,
                          process_lidar: bool = True,
                          compare_baseline: bool = True,
                          visualize: bool = True):
    """
    Complete processing pipeline for Pohang dataset
    
    Args:
        dataset_path: Path to Pohang dataset
        process_lidar: Whether to process LiDAR data
        compare_baseline: Whether to compare with baseline
        visualize: Whether to create visualizations
    """
    # Initialize processor
    processor = PohangLidarProcessor(dataset_path)
    
    # Run INS/GPS fusion
    logger.info("=" * 60)
    logger.info("Starting Pohang dataset processing")
    logger.info("=" * 60)
    
    fusion_results = processor.run_ins_gps_fusion()
    
    # Compare with baseline if requested
    if compare_baseline:
        logger.info("\nComparing with baseline trajectory...")
        metrics = processor.compare_with_baseline()
    
    # Process LiDAR data if requested
    if process_lidar:
        logger.info("\nProcessing LiDAR data...")
        
        # Example: process a few LiDAR scans
        # In practice, you would get actual LiDAR file list
        lidar_files = ["scan_001.bin", "scan_002.bin", "scan_003.bin"]
        timestamps = [fusion_results['time'][0], 
                     fusion_results['time'][100],
                     fusion_results['time'][200]]
        
        lidar_results = processor.process_lidar_sequence(
            lidar_files,
            timestamps,
            sensor_name='lidar_front',
            save_georeferenced=True,
            output_dir="./georeferenced_lidar"
        )
        
        logger.info(f"Processed {len(lidar_files)} LiDAR scans")
        logger.info(f"Sensor trajectory spans: "
                   f"{lidar_results['sensor_positions'].min(axis=0)} to "
                   f"{lidar_results['sensor_positions'].max(axis=0)}")
    
    # Visualize results
    if visualize:
        logger.info("\nGenerating visualizations...")
        processor.visualize_results(
            show_lidar_trajectory=True,
            save_plots=True,
            output_dir="./results"
        )
    
    logger.info("\nProcessing complete!")
    
    return processor


if __name__ == "__main__":
    # Example usage
    dataset_path = "..\\pohang_dataset"
    processor = process_pohang_dataset(
        dataset_path,
        process_lidar=True,
        compare_baseline=True,
        visualize=True
    )
