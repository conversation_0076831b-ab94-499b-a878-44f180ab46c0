def plot_pohang_trajectory(loader: PohangDatasetLoader):
    """Plot the trajectory from Pohang dataset"""
    import matplotlib.pyplot as plt
    
    if loader.gps_data is not None:
        ned_pos = loader.gps_data['position_ned']
        plt.figure(figsize=(10, 8))
        plt.plot(ned_pos[:, 1], ned_pos[:, 0], 'b-', label='GPS Trajectory')
        plt.xlabel('East (m)')
        plt.ylabel('North (m)')
        plt.title('Pohang Canal Trajectory')
        plt.axis('equal')
        plt.grid(True)
        plt.legend()
        plt.show()