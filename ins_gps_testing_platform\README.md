# INS/GPS Fusion Testing Platform

A comprehensive, production-ready testing platform for evaluating INS/GPS fusion algorithms in boat motion compensation applications.

## Features

### 🎯 Complete Testing Suite
- **7 Trajectory Types**: Circular, Figure-8, Square, Straight, Spiral, Survey Lines, Coastal Patrol
- **3 IMU Quality Levels**: Consumer, Navigation, Survey grade sensors
- **5 Environment Conditions**: From ideal to extreme GPS interference scenarios
- **Selective Sensor Participation**: Enable/disable any sensor during estimation

### 🧮 Advanced Simulation
- **28-State Extended Kalman Filter**: Production-ready INS/GPS fusion
- **Complete Sensor Models**: Accelerometer, gyroscope, magnetometer, GPS position/velocity
- **Realistic Noise Models**: Based on actual sensor specifications
- **Configurable Sampling Rates**: Individual control for each sensor type

### 📊 Comprehensive Analysis
- **2D/3D Trajectory Visualization**: True vs estimated paths with GPS measurements
- **Error Analysis**: Position, velocity, orientation errors with statistics
- **Interactive Plots**: Zoom, pan, rotate with professional visualization
- **Statistical Reports**: RMS, max, mean errors with 95th percentile analysis

### 💾 Data Management
- **JSON Configuration**: Human-readable, version-controlled settings
- **Template System**: Pre-defined scenarios with auto-loading
- **Multiple Export Formats**: CSV, MATLAB, HDF5 for external analysis
- **Industry Standards**: Compatible with common INS/GPS data formats

## Quick Start

### Installation

1. **Install Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the Application**:
   ```bash
   python main.py
   ```

### First Steps

1. **Configure Trajectory**: Choose from 7 trajectory types in Configuration tab
2. **Set Sensor Parameters**: Select IMU quality and individual sensor rates
3. **Choose Environment**: Set GPS accuracy and interference conditions
4. **Run Simulation**: Click "Run Simulation" to process complete scenario
5. **Analyze Results**: Review plots and statistics in Visualization tab

## System Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    GUI Layer (Tkinter)                         │
├─────────────────────────────────────────────────────────────────┤
│  Tab 1: Configuration    │  Tab 2: Visualization & Results     │
└─────────────────────────────────────────────────────────────────┘
                                    │
                                    ▼
┌─────────────────────────────────────────────────────────────────┐
│                 Core Processing Layer                           │
├─────────────────────────────────────────────────────────────────┤
│  Simulation Engine  │  Fusion Engine  │  Analysis Engine       │
│  - Trajectory Gen.  │  - INS Filter   │  - Error Computation   │
│  - Sensor Models    │  - Integration  │  - Statistics          │
│  - Noise Models     │  - Fusion Logic │  - Visualization Data  │
└─────────────────────────────────────────────────────────────────┘
```

## Configuration Examples

### Circular Trajectory - Navigation Grade
```json
{
  "trajectory": {
    "trajectory_type": "CIRCULAR",
    "duration": 60.0,
    "vessel_speed": 5.0,
    "vessel_length": 20.0,
    "center_lat": 42.3601,
    "center_lon": -71.0589,
    "center_alt": 0.0,
    "radius": 100.0
  },
  "sensors": {
    "imu_quality": "NAVIGATION",
    "accelerometer_rate": 100.0,
    "gps_position_rate": 10.0
  },
  "environment": {
    "condition_template": "GOOD",
    "gps_horizontal_accuracy": 2.0
  }
}
```

### Survey Mission - High Precision
```json
{
  "trajectory": {
    "trajectory_type": "SURVEY_LINES",
    "vessel_speed": 4.0,
    "vessel_length": 20.0,
    "center_lat": 42.3601,
    "center_lon": -71.0589,
    "center_alt": 0.0,
    "survey_spacing": 50.0,
    "survey_lines": 5
  },
  "sensors": {
    "imu_quality": "SURVEY",
    "accelerometer_rate": 200.0
  },
  "environment": {
    "condition_template": "IDEAL"
  }
}
```

## Trajectory Types

| Type | Description | Parameters |
|------|-------------|------------|
| **CIRCULAR** | Constant radius circular path | radius, speed |
| **FIGURE8** | Figure-8 pattern (lemniscate) | width, height |
| **SQUARE** | Square with sharp corners | size |
| **STRAIGHT** | Straight line motion | speed, duration |
| **SPIRAL** | Expanding/contracting spiral | turns, pitch |
| **SURVEY_LINES** | Lawnmower survey pattern | spacing, lines |
| **COASTAL_PATROL** | Irregular coastal patrol | waypoints, irregularity |

## Sensor Quality Specifications

| Quality | Accelerometer | Gyroscope | Magnetometer | Applications |
|---------|---------------|-----------|--------------|--------------|
| **Consumer** | 1.0 m/s² | 0.1 rad/s | 2.0 µT | Smartphones, basic navigation |
| **Navigation** | 0.1 m/s² | 0.01 rad/s | 0.5 µT | Automotive, marine systems |
| **Survey** | 0.01 m/s² | 0.001 rad/s | 0.1 µT | High-precision applications |

## Error Analysis Metrics

- **RMS Position Error**: Root-mean-square 2D position accuracy
- **Maximum Position Error**: Worst-case position deviation
- **95th Percentile Error**: Statistical confidence metric
- **Velocity Error**: Speed and direction accuracy
- **Orientation Error**: Heading and attitude accuracy

## File Structure

```
ins_gps_testing_platform/
├── main.py                    # Application entry point
├── requirements.txt           # Python dependencies
├── config/                    # Configuration management
│   ├── configuration_manager.py
│   └── templates/            # JSON templates
├── gui/                      # User interface
│   ├── main_gui.py
│   ├── configuration_tab.py
│   └── visualization_tab.py
├── simulation/               # Trajectory and sensor simulation
│   └── simulation_engine.py
├── fusion/                   # INS filter integration
│   ├── fusion_engine.py
│   └── saeid_gps_ins_ekf.py  # 28-state EKF implementation
├── analysis/                 # Error analysis and statistics
│   └── analysis_engine.py
├── data/                     # Data models and export
│   └── data_models.py
├── utils/                    # Utilities and helpers
│   ├── constants.py
│   ├── helpers.py
│   └── coordinate_transforms.py
└── exports/                  # Generated data and plots
```

## Technical Details

### INS Filter Specifications
- **28-State Extended Kalman Filter**: Complete implementation
- **State Vector**: Position, velocity, acceleration, orientation, angular velocity, sensor biases, magnetic field
- **Measurement Models**: Accelerometer, gyroscope, magnetometer, GPS position/velocity
- **Coordinate Frames**: NED navigation frame, body-fixed frame
- **Geodetic Transforms**: WGS84 ellipsoid model

### Numerical Methods
- **Quaternion Kinematics**: Exact integration using matrix exponential
- **Covariance Propagation**: Joseph form for numerical stability
- **Sensor Fusion**: Selective participation without regenerating data
- **Error Computation**: Proper statistical analysis with confidence intervals

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all dependencies are installed via `pip install -r requirements.txt`
2. **GUI Not Responding**: Check that tkinter is available (usually included with Python)
3. **Slow Performance**: Reduce trajectory duration or sampling rate for faster processing
4. **Memory Issues**: Large simulations may require more RAM; consider shorter durations

### Performance Tips

- **Trajectory Duration**: Start with 300s for initial testing
- **Sampling Rates**: 100Hz for IMU, 10Hz for GPS is usually sufficient
- **Sensor Participation**: Disable unused sensors to speed up processing
- **Visualization**: Use "Clear Results" between runs to free memory

## Support and Development

This is a complete, standalone testing platform generated specifically for INS/GPS fusion evaluation. The system is designed to be:

- **Self-contained**: No external dependencies beyond Python packages
- **Configurable**: All parameters adjustable via GUI or JSON files
- **Extensible**: Clean architecture allows easy addition of new features
- **Professional**: Production-ready code with comprehensive error handling

For additional trajectory types, sensor models, or analysis features, the modular architecture supports straightforward extensions.

## License

This software is provided as-is for research and development purposes in marine navigation and motion compensation applications.
