"""Configuration Management System"""
import json
import os
import numpy as np
from pathlib import Path
from dataclasses import dataclass, asdict
from typing import Dict, Any, Optional, List
import logging

logger = logging.getLogger(__name__)

@dataclass
class TrajectoryConfig:
    """Trajectory configuration parameters"""
    trajectory_type: str = "CIRCULAR"
    # Common parameters (shared across all trajectories)
    duration: float = 60.0  # seconds
    sampling_rate: float = 100.0  # Hz
    vessel_speed: float = 5.0  # m/s (moved from trajectory-specific)
    vessel_length: float = 20.0  # meters (vessel length)
    # LGF (Local Geodetic Frame) reference location for coordinate transformations
    center_lat: float = 42.3601  # degrees (LGF reference latitude)
    center_lon: float = -71.0589  # degrees (LGF reference longitude)
    center_alt: float = 0.0  # meters (LGF reference altitude)
    # Trajectory-specific parameters (will be overridden by template files)
    radius: float = 100.0  # meters
    min_turn_radius_factor: float = 3.0  # minimum turn radius = factor * vessel_length (trajectory-specific)
    # Figure-8 specific
    figure8_width: float = 200.0  # meters
    figure8_height: float = 100.0  # meters
    # Square specific
    square_size: float = 200.0  # meters
    # Spiral specific
    spiral_turns: int = 3
    spiral_pitch: float = 50.0  # meters per turn
    # Survey lines specific
    survey_spacing: float = 50.0  # meters
    survey_lines: int = 5
    line_length: float = 400.0  # meters (length of each survey line)
    # Coastal patrol specific
    coastal_waypoints: int = 8
    coastal_irregularity: float = 0.2  # randomness factor

@dataclass
class SensorConfig:
    """Sensor configuration parameters"""
    # IMU Quality template
    imu_quality: str = "NAVIGATION"  # CONSUMER, NAVIGATION, SURVEY
    
    # Individual sensor rates (Hz)
    accelerometer_rate: float = 100.0
    gyroscope_rate: float = 100.0
    magnetometer_rate: float = 20.0
    gps_position_rate: float = 10.0
    gps_velocity_rate: float = 10.0
    
    # Sensor participation (for selective fusion)
    use_accelerometer: bool = True
    use_gyroscope: bool = True
    use_magnetometer: bool = False  # Off by default - user can enable if needed
    use_gps_position: bool = True
    use_gps_velocity: bool = False  # Off by default - user can enable if needed
    
    # User-friendly gyroscope specifications (standard IMU specs)
    gyroscope_arw: float = 0.003  # deg/√hr - Angle Random Walk
    gyroscope_bias_stability: float = 0.0065  # deg/hr - Bias Stability

    # Noise parameters (will be set based on quality template or calculated from specs)
    accelerometer_noise: float = 0.1  # m/s²
    gyroscope_noise: float = 0.01  # rad/s (calculated from ARW)
    gyroscope_bias_process_noise: Optional[float] = None  # rad/s/√s (calculated from bias stability)
    magnetometer_noise: float = 0.5  # µT
    gps_position_noise: float = 2.0  # meters
    gps_velocity_noise: float = 0.1  # m/s
    # 3D GPS position noise [north_var, east_var, down_var] - optional for enhanced accuracy
    gps_position_noise_3d: Optional[List[float]] = None

@dataclass
class EnvironmentConfig:
    """Environmental conditions configuration"""
    condition_template: str = "GOOD"  # IDEAL, GOOD, MODERATE, POOR, EXTREME
    
    # GPS accuracy (meters)
    gps_horizontal_accuracy: float = 2.0
    gps_vertical_accuracy: float = 3.0
    gps_velocity_accuracy: float = 0.1
    
    # Environmental factors
    magnetic_declination: float = -14.0  # degrees
    magnetic_inclination: float = 70.0  # degrees
    magnetic_field_strength: float = 50.0  # µT
    
    # Disturbance factors
    gps_outage_probability: float = 0.0  # 0.0 to 1.0
    multipath_factor: float = 1.0  # multiplier for GPS noise

def convert_gyroscope_specs_to_noise(arw_deg_sqrt_hr: float,
                                   bias_stability_deg_hr: float,
                                   sampling_rate_hz: float) -> tuple:
    """
    Convert standard gyroscope specifications to internal noise parameters

    Args:
        arw_deg_sqrt_hr: Angle Random Walk in deg/√hr
        bias_stability_deg_hr: Bias Stability in deg/hr
        sampling_rate_hz: Gyroscope sampling rate in Hz

    Returns:
        tuple: (measurement_noise_rad_s, bias_process_noise_rad_s)
    """
    # Convert ARW to measurement noise
    # ARW [deg/√hr] -> noise [rad/s]
    arw_rad_sqrt_s = arw_deg_sqrt_hr * (np.pi/180) / np.sqrt(3600)  # Convert to rad/√s
    measurement_noise = arw_rad_sqrt_s / np.sqrt(sampling_rate_hz)  # Scale by sampling rate

    # Convert bias stability to process noise
    # Bias stability [deg/hr] -> bias process noise [rad/s]
    bias_stability_rad_s = bias_stability_deg_hr * (np.pi/180) / 3600  # Convert to rad/s
    # Model as random walk: process noise = bias_stability / sqrt(correlation_time)
    # Use 1 hour correlation time for bias stability
    bias_process_noise = bias_stability_rad_s / np.sqrt(3600)  # rad/s/√s

    return measurement_noise, bias_process_noise

def convert_noise_to_gyroscope_specs(measurement_noise_rad_s: float,
                                   bias_process_noise_rad_s: float,
                                   sampling_rate_hz: float) -> tuple:
    """
    Convert internal noise parameters back to standard gyroscope specifications

    Args:
        measurement_noise_rad_s: Measurement noise in rad/s
        bias_process_noise_rad_s: Bias process noise in rad/s/√s
        sampling_rate_hz: Gyroscope sampling rate in Hz

    Returns:
        tuple: (arw_deg_sqrt_hr, bias_stability_deg_hr)
    """
    # Convert measurement noise to ARW
    arw_rad_sqrt_s = measurement_noise_rad_s * np.sqrt(sampling_rate_hz)
    arw_deg_sqrt_hr = arw_rad_sqrt_s * (180/np.pi) * np.sqrt(3600)

    # Convert bias process noise to bias stability
    bias_stability_rad_s = bias_process_noise_rad_s * np.sqrt(3600)
    bias_stability_deg_hr = bias_stability_rad_s * (180/np.pi) * 3600

    return arw_deg_sqrt_hr, bias_stability_deg_hr

class ConfigurationManager:
    """Manages all configuration data and templates"""
    
    def __init__(self):
        self.config_dir = Path("config")
        self.templates_dir = Path("config/templates")
        self.current_config = {
            "trajectory": TrajectoryConfig(),
            "sensors": SensorConfig(),
            "environment": EnvironmentConfig()
        }
        
    def load_trajectory_template(self, template_name: str) -> TrajectoryConfig:
        """Load trajectory configuration template"""
        template_path = self.templates_dir / "trajectories" / f"{template_name.lower()}.json"
        try:
            with open(template_path, 'r') as f:
                data = json.load(f)
            config = TrajectoryConfig(**data)
            self.current_config["trajectory"] = config
            logger.info(f"Loaded trajectory template: {template_name}")
            return config
        except Exception as e:
            logger.error(f"Failed to load trajectory template {template_name}: {e}")
            return TrajectoryConfig()
    
    def load_sensor_template(self, template_name: str) -> SensorConfig:
        """Load sensor configuration template"""
        template_path = self.templates_dir / "sensors" / f"{template_name.lower()}.json"
        try:
            with open(template_path, 'r') as f:
                data = json.load(f)
            config = SensorConfig(**data)
            self.current_config["sensors"] = config
            logger.info(f"Loaded sensor template: {template_name}")
            return config
        except Exception as e:
            logger.error(f"Failed to load sensor template {template_name}: {e}")
            return SensorConfig()
    
    def load_environment_template(self, template_name: str) -> EnvironmentConfig:
        """Load environment configuration template"""
        template_path = self.templates_dir / "environments" / f"{template_name.lower()}.json"
        try:
            with open(template_path, 'r') as f:
                data = json.load(f)
            config = EnvironmentConfig(**data)
            self.current_config["environment"] = config
            logger.info(f"Loaded environment template: {template_name}")
            return config
        except Exception as e:
            logger.error(f"Failed to load environment template {template_name}: {e}")
            return EnvironmentConfig()
    
    def save_configuration(self, filename: str) -> bool:
        """Save current configuration to file"""
        try:
            config_data = {
                "trajectory": asdict(self.current_config["trajectory"]),
                "sensors": asdict(self.current_config["sensors"]),
                "environment": asdict(self.current_config["environment"])
            }
            
            filepath = self.config_dir / f"{filename}.json"
            with open(filepath, 'w') as f:
                json.dump(config_data, f, indent=4)
            
            logger.info(f"Configuration saved to: {filepath}")
            return True
        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False
    
    def load_configuration(self, filename: str) -> bool:
        """Load configuration from file"""
        try:
            filepath = self.config_dir / f"{filename}.json"
            with open(filepath, 'r') as f:
                data = json.load(f)
            
            self.current_config["trajectory"] = TrajectoryConfig(**data["trajectory"])
            self.current_config["sensors"] = SensorConfig(**data["sensors"])
            self.current_config["environment"] = EnvironmentConfig(**data["environment"])
            
            logger.info(f"Configuration loaded from: {filepath}")
            return True
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return False
    
    def get_trajectory_config(self) -> TrajectoryConfig:
        """Get current trajectory configuration"""
        return self.current_config["trajectory"]
    
    def get_sensor_config(self) -> SensorConfig:
        """Get current sensor configuration"""
        return self.current_config["sensors"]
    
    def get_environment_config(self) -> EnvironmentConfig:
        """Get current environment configuration"""
        return self.current_config["environment"]
    
    def update_sensor_quality(self, quality: str):
        """Update sensor parameters based on quality template from JSON file"""
        # Load sensor template from JSON file
        self.load_sensor_template(quality)
        logger.info(f"Updated sensor quality to: {quality}")

    def _update_gyroscope_noise_from_specs(self, sensor_config: SensorConfig):
        """Update gyroscope_noise parameter from ARW and bias stability specs"""
        measurement_noise, bias_process_noise = convert_gyroscope_specs_to_noise(
            sensor_config.gyroscope_arw,
            sensor_config.gyroscope_bias_stability,
            sensor_config.gyroscope_rate
        )
        sensor_config.gyroscope_noise = measurement_noise
        sensor_config.gyroscope_bias_process_noise = bias_process_noise

        logger.info(f"Updated gyroscope noise: ARW={sensor_config.gyroscope_arw} deg/√hr -> "
                   f"measurement_noise={measurement_noise:.2e} rad/s, "
                   f"bias_process_noise={bias_process_noise:.2e} rad/s/√s")

    def update_gyroscope_specs(self, arw_deg_sqrt_hr: float, bias_stability_deg_hr: float):
        """Update gyroscope specifications and recalculate noise parameters"""
        sensor_config = self.current_config["sensors"]
        sensor_config.gyroscope_arw = arw_deg_sqrt_hr
        sensor_config.gyroscope_bias_stability = bias_stability_deg_hr
        self._update_gyroscope_noise_from_specs(sensor_config)

    def update_environment_quality(self, condition: str):
        """Update environment parameters based on condition template from JSON file"""
        # Load environment template from JSON file
        self.load_environment_template(condition)
        logger.info(f"Updated environment quality to: {condition}")
