"""System Constants and Parameters"""

# Earth model constants
EARTH_RADIUS = 6378137.0  # meters (WGS84 equatorial radius)
EARTH_FLATTENING = 1.0 / 298.257223563  # WGS84 flattening
EARTH_ROTATION_RATE = 7.2921159e-5  # rad/s
STANDARD_GRAVITY = 9.80665  # m/s²

# Coordinate system conventions
COORDINATE_FRAMES = {
    'NED': 'North-East-Down',
    'BODY': 'Body-fixed frame',
    'LLA': 'Latitude-Longitude-Altitude'
}

# Sensor quality templates
SENSOR_QUALITY_SPECS = {
    'CONSUMER': {
        'accelerometer_noise': 1.0,  # m/s²
        'gyroscope_noise': 0.1,     # rad/s
        'magnetometer_noise': 2.0,  # µT
        'description': 'Consumer-grade IMU (smartphone quality)'
    },
    'NAVIGATION': {
        'accelerometer_noise': 0.1,  # m/s²
        'gyroscope_noise': 0.01,    # rad/s
        'magnetometer_noise': 0.5,  # µT
        'description': 'Navigation-grade IMU (automotive/marine)'
    },
    'SURVEY': {
        'accelerometer_noise': 0.01,  # m/s²
        'gyroscope_noise': 0.001,    # rad/s
        'magnetometer_noise': 0.1,   # µT
        'description': 'Survey-grade IMU (high-precision applications)'
    }
}

# Environment condition templates
ENVIRONMENT_CONDITIONS = {
    'IDEAL': {
        'gps_horizontal_accuracy': 0.5,  # meters
        'gps_vertical_accuracy': 1.0,    # meters
        'gps_outage_probability': 0.0,   # 0-1
        'multipath_factor': 1.0,         # multiplier
        'description': 'Perfect conditions, no interference'
    },
    'GOOD': {
        'gps_horizontal_accuracy': 2.0,
        'gps_vertical_accuracy': 3.0,
        'gps_outage_probability': 0.01,
        'multipath_factor': 1.2,
        'description': 'Good conditions, minimal interference'
    },
    'MODERATE': {
        'gps_horizontal_accuracy': 5.0,
        'gps_vertical_accuracy': 8.0,
        'gps_outage_probability': 0.05,
        'multipath_factor': 1.5,
        'description': 'Moderate conditions, some interference'
    },
    'POOR': {
        'gps_horizontal_accuracy': 10.0,
        'gps_vertical_accuracy': 15.0,
        'gps_outage_probability': 0.1,
        'multipath_factor': 2.0,
        'description': 'Poor conditions, significant interference'
    },
    'EXTREME': {
        'gps_horizontal_accuracy': 20.0,
        'gps_vertical_accuracy': 30.0,
        'gps_outage_probability': 0.2,
        'multipath_factor': 3.0,
        'description': 'Extreme conditions, heavy interference'
    }
}

# Note: The above SENSOR_QUALITY_SPECS and ENVIRONMENT_CONDITIONS are now obsolete
# All sensor and environment parameters are loaded from JSON template files in:
# - config/templates/sensors/ (consumer.json, navigation.json, survey.json)
# - config/templates/environments/ (ideal.json, good.json, moderate.json, poor.json, extreme.json)
# This eliminates hardcoded values and allows for easier configuration management

# Trajectory type specifications
TRAJECTORY_TYPES = {
    'CIRCULAR': 'Circular trajectory with constant radius',
    'FIGURE8': 'Figure-8 trajectory (lemniscate)',
    'SQUARE': 'Square trajectory with sharp corners',
    'STRAIGHT': 'Straight line trajectory',
    'SPIRAL': 'Spiral trajectory with increasing/decreasing radius',
    'SURVEY_LINES': 'Survey lines (lawnmower pattern)',
    'COASTAL_PATROL': 'Irregular coastal patrol pattern'
}

# Default magnetic field (Boston area)
DEFAULT_MAGNETIC_FIELD_NED = [20.5, -4.2, 51.8]  # µT

# Color scheme for consistent visualization
VISUALIZATION_COLORS = {
    'true_trajectory': 'blue',
    'estimated_trajectory': 'red',
    'gps_measurements': 'green',
    'start_point': 'green',
    'end_point': 'red',
    'error_plot': 'red',
    'background_grid': 'lightgray'
}
