#!/usr/bin/env python3
"""
Test script to verify enhanced visualization system
"""

import sys
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import os

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def create_mock_data():
    """Create mock trajectory data for testing visualization"""
    
    # Create time series
    t = np.linspace(0, 100, 1000)  # 100 seconds, 1000 samples
    
    # Create a realistic boat trajectory (figure-8 pattern)
    center_x, center_y = 0, 0
    radius = 50
    
    # Estimated trajectory (with some noise/error)
    est_x = radius * np.sin(0.1 * t) + np.random.normal(0, 2, len(t))
    est_y = radius * np.sin(0.2 * t) + np.random.normal(0, 2, len(t))
    est_z = -5 + 2 * np.sin(0.05 * t) + np.random.normal(0, 0.5, len(t))
    
    # Ground truth trajectory (smoother)
    gt_x = radius * np.sin(0.1 * t)
    gt_y = radius * np.sin(0.2 * t)
    gt_z = -5 + 2 * np.sin(0.05 * t)
    
    # Create data dictionaries
    fusion_results = {
        'time': t,
        'position': np.column_stack([est_x, est_y, est_z])
    }
    
    baseline_data = {
        'time': t,
        'position': np.column_stack([gt_x, gt_y, gt_z])
    }
    
    return fusion_results, baseline_data

def test_comprehensive_comparison():
    """Test the comprehensive trajectory comparison visualization"""
    logger.info("Testing comprehensive trajectory comparison...")
    
    # Create mock data
    fusion_results, baseline_data = create_mock_data()
    
    # Mock reference location (Pohang area)
    reference_location = (36.023619907, 129.378011880, 0.0)
    
    # Create a mock processor class to test the visualization method
    class MockProcessor:
        def __init__(self):
            self.fusion_results = fusion_results
            self.baseline_data = baseline_data
            self.reference_location = reference_location
        
        def _plot_comprehensive_comparison(self, output_dir=None):
            """Mock implementation of comprehensive comparison"""
            import matplotlib.pyplot as plt
            from mpl_toolkits.mplot3d import Axes3D
            
            est_pos = self.fusion_results['position']
            baseline_pos = self.baseline_data['position']
            
            # Create comprehensive comparison figure
            fig = plt.figure(figsize=(20, 12))
            fig.suptitle('TEST: Comprehensive Trajectory Comparison', fontsize=16)
            
            # 3D trajectory comparison
            ax1 = fig.add_subplot(231, projection='3d')
            ax1.plot(est_pos[:, 1], est_pos[:, 0], -est_pos[:, 2], 'b-', linewidth=2, label='Estimated', alpha=0.8)
            ax1.plot(baseline_pos[:, 1], baseline_pos[:, 0], -baseline_pos[:, 2], 'r--', linewidth=2, label='Ground Truth', alpha=0.8)
            ax1.set_xlabel('East (m)')
            ax1.set_ylabel('North (m)')
            ax1.set_zlabel('Up (m)')
            ax1.set_title('3D Trajectory Comparison')
            ax1.legend()
            
            # 2D top view
            ax2 = fig.add_subplot(232)
            ax2.plot(est_pos[:, 1], est_pos[:, 0], 'b-', linewidth=2, label='Estimated', alpha=0.8)
            ax2.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r--', linewidth=2, label='Ground Truth', alpha=0.8)
            ax2.set_xlabel('East (m)')
            ax2.set_ylabel('North (m)')
            ax2.set_title('2D Trajectory (Top View)')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            ax2.axis('equal')
            
            plt.tight_layout()
            
            if output_dir:
                os.makedirs(output_dir, exist_ok=True)
                comparison_file = os.path.join(output_dir, "test_comprehensive_comparison.png")
                plt.savefig(comparison_file, dpi=300, bbox_inches='tight')
                logger.info(f"✓ Test comparison saved to: {os.path.abspath(comparison_file)}")
            
            plt.close()
            return True
    
    # Test the visualization
    processor = MockProcessor()
    result = processor._plot_comprehensive_comparison("test_outputs")
    
    return result

def test_error_analysis():
    """Test the detailed error analysis visualization"""
    logger.info("Testing detailed error analysis...")
    
    # Create mock data
    fusion_results, baseline_data = create_mock_data()
    
    # Calculate errors
    est_pos = fusion_results['position']
    baseline_pos = baseline_data['position']
    pos_errors = est_pos - baseline_pos
    
    # Create error analysis figure
    fig = plt.figure(figsize=(16, 12))
    fig.suptitle('TEST: Detailed Error Analysis', fontsize=16)
    
    # Position error components
    ax1 = fig.add_subplot(221)
    time = fusion_results['time']
    ax1.plot(time, pos_errors[:, 0], 'r-', label='North Error', alpha=0.8)
    ax1.plot(time, pos_errors[:, 1], 'g-', label='East Error', alpha=0.8)
    ax1.plot(time, pos_errors[:, 2], 'b-', label='Down Error', alpha=0.8)
    ax1.set_xlabel('Time (s)')
    ax1.set_ylabel('Position Error (m)')
    ax1.set_title('Position Error Components')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Error statistics
    ax2 = fig.add_subplot(222)
    ax2.axis('off')
    
    horizontal_errors = np.sqrt(pos_errors[:, 0]**2 + pos_errors[:, 1]**2)
    vertical_errors = np.abs(pos_errors[:, 2])
    
    stats_text = "Error Statistics:\n\n"
    stats_text += f"Horizontal RMS: {np.sqrt(np.mean(horizontal_errors**2)):.3f} m\n"
    stats_text += f"Vertical RMS: {np.sqrt(np.mean(vertical_errors**2)):.3f} m\n"
    stats_text += f"Max Horizontal: {np.max(horizontal_errors):.3f} m\n"
    stats_text += f"Max Vertical: {np.max(vertical_errors):.3f} m"
    
    ax2.text(0.05, 0.95, stats_text, transform=ax2.transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcyan', alpha=0.8))
    
    plt.tight_layout()
    
    os.makedirs("test_outputs", exist_ok=True)
    error_file = "test_outputs/test_error_analysis.png"
    plt.savefig(error_file, dpi=300, bbox_inches='tight')
    logger.info(f"✓ Test error analysis saved to: {os.path.abspath(error_file)}")
    
    plt.close()
    return True

def test_coordinate_frame_visualization():
    """Test coordinate frame alignment visualization"""
    logger.info("Testing coordinate frame alignment...")
    
    # Create mock data
    fusion_results, baseline_data = create_mock_data()
    reference_location = (36.023619907, 129.378011880, 0.0)
    
    # Create coordinate frame figure
    fig = plt.figure(figsize=(16, 10))
    fig.suptitle('TEST: Coordinate Frame Alignment', fontsize=16)
    
    est_pos = fusion_results['position']
    baseline_pos = baseline_data['position']
    
    # Trajectory overlay
    ax1 = fig.add_subplot(221)
    ax1.plot(est_pos[:, 1], est_pos[:, 0], 'b-', linewidth=2, label='Estimated', alpha=0.8)
    ax1.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r--', linewidth=2, label='Ground Truth', alpha=0.8)
    
    # Mark coordinate system
    ax1.scatter([0], [0], c='black', s=100, marker='+', linewidth=3, label='LGF Origin')
    ax1.arrow(0, 0, 20, 0, head_width=5, head_length=5, fc='red', ec='red', alpha=0.7)
    ax1.arrow(0, 0, 0, 20, head_width=5, head_length=5, fc='green', ec='green', alpha=0.7)
    ax1.text(25, 0, 'East (+Y)', fontsize=10, color='red')
    ax1.text(0, 25, 'North (+X)', fontsize=10, color='green')
    
    ax1.set_xlabel('East (m)')
    ax1.set_ylabel('North (m)')
    ax1.set_title('Trajectory in Local Geodetic Frame')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.axis('equal')
    
    # Coordinate system info
    ax2 = fig.add_subplot(222)
    ax2.axis('off')
    
    coord_info = "Coordinate System:\n\n"
    coord_info += f"LGF Origin: ({reference_location[0]:.6f}°, {reference_location[1]:.6f}°)\n"
    coord_info += "X-axis: North\n"
    coord_info += "Y-axis: East\n"
    coord_info += "Z-axis: Down\n\n"
    coord_info += "Both trajectories in same frame"
    
    ax2.text(0.05, 0.95, coord_info, transform=ax2.transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    os.makedirs("test_outputs", exist_ok=True)
    frame_file = "test_outputs/test_coordinate_frame.png"
    plt.savefig(frame_file, dpi=300, bbox_inches='tight')
    logger.info(f"✓ Test coordinate frame saved to: {os.path.abspath(frame_file)}")
    
    plt.close()
    return True

def main():
    """Run all visualization tests"""
    logger.info("="*60)
    logger.info("ENHANCED VISUALIZATION SYSTEM TESTS")
    logger.info("="*60)
    
    tests = [
        ("Comprehensive Trajectory Comparison", test_comprehensive_comparison),
        ("Detailed Error Analysis", test_error_analysis),
        ("Coordinate Frame Alignment", test_coordinate_frame_visualization)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"Test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info("\n" + "="*60)
    logger.info("TEST SUMMARY")
    logger.info("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nPassed: {passed}/{len(results)} tests")
    logger.info(f"📁 Test outputs saved to: {os.path.abspath('test_outputs')}")
    
    if passed == len(results):
        logger.info("✓ All visualization tests passed!")
        return True
    else:
        logger.warning("⚠ Some tests failed")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
