#!/usr/bin/env python3
"""
Test script to understand and fix coordinate system alignment between 
estimated trajectory and ground truth baseline
"""

import sys
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
import os

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def analyze_coordinate_systems():
    """Analyze the coordinate systems used in the Pohang dataset"""
    
    logger.info("Analyzing Pohang dataset coordinate systems...")
    
    # Try to load actual Pohang data if available
    dataset_path = Path("../../../MOSTAFAVI-02.ffgg.ulaval.ca/Doodman/lidar_gps_ins")
    
    if not dataset_path.exists():
        logger.info("Pohang dataset not found - creating mock analysis")
        return analyze_mock_coordinate_systems()
    
    try:
        from pohang.pohang_analizor import PohangDatasetLoader
        
        loader = PohangDatasetLoader(dataset_path)
        loader.load_dataset()
        
        # Analyze GPS data coordinate system
        if loader.gps_data is not None:
            gps_pos = loader.gps_data.get('position_ned')
            if gps_pos is not None:
                logger.info("GPS Data Analysis:")
                logger.info(f"  GPS NED Range: N=[{gps_pos[:, 0].min():.1f}, {gps_pos[:, 0].max():.1f}]")
                logger.info(f"                 E=[{gps_pos[:, 1].min():.1f}, {gps_pos[:, 1].max():.1f}]")
                logger.info(f"                 D=[{gps_pos[:, 2].min():.1f}, {gps_pos[:, 2].max():.1f}]")
                logger.info(f"  GPS Center: [{np.mean(gps_pos, axis=0)}]")
        
        # Analyze baseline data coordinate system
        if loader.baseline_data is not None:
            baseline_pos = loader.baseline_data.get('position')
            if baseline_pos is not None:
                logger.info("Baseline Data Analysis:")
                logger.info(f"  Baseline Range: N=[{baseline_pos[:, 0].min():.1f}, {baseline_pos[:, 0].max():.1f}]")
                logger.info(f"                  E=[{baseline_pos[:, 1].min():.1f}, {baseline_pos[:, 1].max():.1f}]")
                logger.info(f"                  D=[{baseline_pos[:, 2].min():.1f}, {baseline_pos[:, 2].max():.1f}]")
                logger.info(f"  Baseline Center: [{np.mean(baseline_pos, axis=0)}]")
                
                # Check if baseline is in local coordinates (small values) or absolute coordinates
                baseline_magnitudes = np.abs(baseline_pos).mean(axis=0)
                if baseline_magnitudes[0] > 100000 or baseline_magnitudes[1] > 100000:
                    logger.info("  Baseline appears to be in UTM/absolute coordinates")
                else:
                    logger.info("  Baseline appears to be in local coordinates")
        
        # Analyze LGF reference
        ref_location = loader.get_lgf_reference_location()
        logger.info(f"LGF Reference: {ref_location}")
        
        return True
        
    except Exception as e:
        logger.error(f"Error analyzing real dataset: {e}")
        return analyze_mock_coordinate_systems()

def analyze_mock_coordinate_systems():
    """Analyze coordinate systems with mock data"""
    
    logger.info("Creating mock coordinate system analysis...")
    
    # Mock GPS data in LLA, converted to NED
    mock_gps_lla = np.array([
        [36.023, 129.378, 5.0],
        [36.024, 129.379, 6.0],
        [36.025, 129.380, 7.0],
        [36.022, 129.377, 4.0]
    ])
    
    # Mock baseline data - could be in different coordinate system
    # Scenario 1: Baseline in local coordinates (small values)
    mock_baseline_local = np.array([
        [0, 0, -5],
        [100, 50, -6],
        [200, 100, -7],
        [-100, -50, -4]
    ])
    
    # Scenario 2: Baseline in UTM coordinates (large values)
    mock_baseline_utm = np.array([
        [500000, 4000000, -5],
        [500100, 4000050, -6],
        [500200, 4000100, -7],
        [499900, 3999950, -4]
    ])
    
    logger.info("Mock GPS LLA data:")
    logger.info(f"  Range: Lat=[{mock_gps_lla[:, 0].min():.6f}, {mock_gps_lla[:, 0].max():.6f}]")
    logger.info(f"         Lon=[{mock_gps_lla[:, 1].min():.6f}, {mock_gps_lla[:, 1].max():.6f}]")
    logger.info(f"         Alt=[{mock_gps_lla[:, 2].min():.1f}, {mock_gps_lla[:, 2].max():.1f}]")
    
    # Convert GPS to NED using different reference points
    from utils.coordinate_transforms import lla_to_ned
    
    # Reference 1: GPS center
    gps_center = np.mean(mock_gps_lla, axis=0)
    ref_center = (gps_center[0], gps_center[1], gps_center[2])
    gps_ned_center_ref = lla_to_ned(mock_gps_lla, ref_center)
    
    # Reference 2: First GPS point
    ref_first = (mock_gps_lla[0, 0], mock_gps_lla[0, 1], mock_gps_lla[0, 2])
    gps_ned_first_ref = lla_to_ned(mock_gps_lla, ref_first)
    
    logger.info("\nGPS converted to NED with center reference:")
    logger.info(f"  Range: N=[{gps_ned_center_ref[:, 0].min():.1f}, {gps_ned_center_ref[:, 0].max():.1f}]")
    logger.info(f"         E=[{gps_ned_center_ref[:, 1].min():.1f}, {gps_ned_center_ref[:, 1].max():.1f}]")
    logger.info(f"         D=[{gps_ned_center_ref[:, 2].min():.1f}, {gps_ned_center_ref[:, 2].max():.1f}]")
    
    logger.info("\nGPS converted to NED with first point reference:")
    logger.info(f"  Range: N=[{gps_ned_first_ref[:, 0].min():.1f}, {gps_ned_first_ref[:, 0].max():.1f}]")
    logger.info(f"         E=[{gps_ned_first_ref[:, 1].min():.1f}, {gps_ned_first_ref[:, 1].max():.1f}]")
    logger.info(f"         D=[{gps_ned_first_ref[:, 2].min():.1f}, {gps_ned_first_ref[:, 2].max():.1f}]")
    
    logger.info("\nBaseline in local coordinates:")
    logger.info(f"  Range: N=[{mock_baseline_local[:, 0].min():.1f}, {mock_baseline_local[:, 0].max():.1f}]")
    logger.info(f"         E=[{mock_baseline_local[:, 1].min():.1f}, {mock_baseline_local[:, 1].max():.1f}]")
    logger.info(f"         D=[{mock_baseline_local[:, 2].min():.1f}, {mock_baseline_local[:, 2].max():.1f}]")
    
    logger.info("\nBaseline in UTM coordinates:")
    logger.info(f"  Range: N=[{mock_baseline_utm[:, 0].min():.1f}, {mock_baseline_utm[:, 0].max():.1f}]")
    logger.info(f"         E=[{mock_baseline_utm[:, 1].min():.1f}, {mock_baseline_utm[:, 1].max():.1f}]")
    logger.info(f"         D=[{mock_baseline_utm[:, 2].min():.1f}, {mock_baseline_utm[:, 2].max():.1f}]")
    
    # Create visualization
    create_coordinate_comparison_plot(gps_ned_center_ref, gps_ned_first_ref, 
                                    mock_baseline_local, mock_baseline_utm)
    
    return True

def create_coordinate_comparison_plot(gps_ned_center, gps_ned_first, baseline_local, baseline_utm):
    """Create visualization comparing different coordinate systems"""
    
    fig = plt.figure(figsize=(16, 12))
    fig.suptitle('Coordinate System Analysis: GPS vs Baseline', fontsize=16)
    
    # Plot 1: GPS with center reference vs baseline local
    ax1 = fig.add_subplot(221)
    ax1.plot(gps_ned_center[:, 1], gps_ned_center[:, 0], 'b-o', linewidth=2, label='GPS (center ref)', alpha=0.8)
    ax1.plot(baseline_local[:, 1], baseline_local[:, 0], 'r--s', linewidth=2, label='Baseline (local)', alpha=0.8)
    ax1.set_xlabel('East (m)')
    ax1.set_ylabel('North (m)')
    ax1.set_title('GPS (Center Ref) vs Baseline (Local)')
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    ax1.axis('equal')
    
    # Plot 2: GPS with first point reference vs baseline local
    ax2 = fig.add_subplot(222)
    ax2.plot(gps_ned_first[:, 1], gps_ned_first[:, 0], 'b-o', linewidth=2, label='GPS (first ref)', alpha=0.8)
    ax2.plot(baseline_local[:, 1], baseline_local[:, 0], 'r--s', linewidth=2, label='Baseline (local)', alpha=0.8)
    ax2.set_xlabel('East (m)')
    ax2.set_ylabel('North (m)')
    ax2.set_title('GPS (First Ref) vs Baseline (Local)')
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    ax2.axis('equal')
    
    # Plot 3: GPS vs baseline UTM (will show huge mismatch)
    ax3 = fig.add_subplot(223)
    ax3.plot(gps_ned_center[:, 1], gps_ned_center[:, 0], 'b-o', linewidth=2, label='GPS (center ref)', alpha=0.8)
    # Scale down UTM for visualization
    baseline_utm_scaled = baseline_utm - np.mean(baseline_utm, axis=0)
    ax3.plot(baseline_utm_scaled[:, 1], baseline_utm_scaled[:, 0], 'r--s', linewidth=2, label='Baseline (UTM-centered)', alpha=0.8)
    ax3.set_xlabel('East (m)')
    ax3.set_ylabel('North (m)')
    ax3.set_title('GPS vs Baseline (UTM centered)')
    ax3.grid(True, alpha=0.3)
    ax3.legend()
    ax3.axis('equal')
    
    # Plot 4: Coordinate system info
    ax4 = fig.add_subplot(224)
    ax4.axis('off')
    
    info_text = "Coordinate System Analysis:\n\n"
    info_text += "Problem: Estimated trajectory and baseline\n"
    info_text += "may be in different coordinate systems.\n\n"
    info_text += "Solutions:\n"
    info_text += "1. Use baseline coordinate system origin\n"
    info_text += "   as LGF reference\n"
    info_text += "2. Convert both to same reference frame\n"
    info_text += "3. Align coordinate systems before comparison\n\n"
    info_text += "Key: LGF origin should correspond to\n"
    info_text += "baseline coordinate system origin (0,0,0)"
    
    ax4.text(0.05, 0.95, info_text, transform=ax4.transAxes, fontsize=11,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
    
    plt.tight_layout()
    
    os.makedirs("test_outputs", exist_ok=True)
    coord_file = "test_outputs/coordinate_system_analysis.png"
    plt.savefig(coord_file, dpi=300, bbox_inches='tight')
    logger.info(f"📊 Coordinate analysis saved to: {os.path.abspath(coord_file)}")
    
    plt.close()

def propose_solution():
    """Propose solution for coordinate system alignment"""
    
    logger.info("\n" + "="*60)
    logger.info("PROPOSED SOLUTION FOR COORDINATE ALIGNMENT")
    logger.info("="*60)
    
    solution_text = """
    PROBLEM:
    - Estimated trajectory uses LGF with GPS-derived origin
    - Baseline trajectory is in its own coordinate system
    - These may not align, causing visualization issues
    
    SOLUTION:
    1. Determine baseline coordinate system type:
       - If baseline has small coordinates: already in local frame
       - If baseline has large coordinates: in UTM/absolute frame
    
    2. Set LGF origin to align with baseline:
       - Find GPS location that corresponds to baseline origin
       - Use this as LGF reference for estimation
    
    3. Ensure both trajectories use same coordinate system:
       - Convert estimated trajectory to baseline frame, OR
       - Convert baseline to estimated frame, OR
       - Convert both to common reference frame
    
    IMPLEMENTATION:
    - Analyze baseline coordinate magnitudes
    - Find GPS point closest to baseline origin
    - Use that GPS location as LGF reference
    - Verify alignment in visualization
    """
    
    logger.info(solution_text)

def main():
    """Run coordinate system analysis"""
    logger.info("="*60)
    logger.info("COORDINATE SYSTEM ALIGNMENT ANALYSIS")
    logger.info("="*60)
    
    try:
        # Analyze coordinate systems
        success = analyze_coordinate_systems()
        
        if success:
            logger.info("✓ Coordinate system analysis completed")
        else:
            logger.warning("⚠ Analysis completed with issues")
        
        # Propose solution
        propose_solution()
        
        return success
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
