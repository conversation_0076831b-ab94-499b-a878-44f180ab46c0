"""Main GUI Window and Tab Management"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import os
import logging
from gui.configuration_tab import SimulationTab
from gui.estimation_tab import EstimationTab
from gui.visualization_tab import VisualizationTab
from gui.pohang_tab import PohangTab
from gui.pohang_results_tab import PohangVisualizationTab
from config.configuration_manager import ConfigurationManager
from simulation.simulation_engine import SimulationEngine
from fusion.fusion_engine import FusionEngine
from analysis.analysis_engine import AnalysisEngine

logger = logging.getLogger(__name__)

class MainGUI:
    """Main application GUI window"""
    
    def __init__(self, root):
        self.root = root
        self.setup_main_window()
        
        # Initialize core components
        self.config_manager = ConfigurationManager()
        self.simulation_engine = SimulationEngine()
        self.fusion_engine = FusionEngine()
        self.analysis_engine = AnalysisEngine()
        
        # Create GUI components
        self.create_menu_bar()
        self.create_main_notebook()
        self.create_status_bar()
        
        logger.info("Main GUI initialized successfully")
    
    def setup_main_window(self):
        """Setup main window properties"""
        self.root.title("INS/GPS Fusion Testing Platform")
        self.root.geometry("1400x900")
        self.root.minsize(1200, 800)
        
        # Set application icon (if available)
        try:
            self.root.iconbitmap("icon.ico")
        except:
            pass
    
    def create_menu_bar(self):
        """Create application menu bar"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # File menu
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Configuration", command=self.new_configuration)
        file_menu.add_command(label="Open Configuration...", command=self.open_configuration)
        file_menu.add_command(label="Save Configuration...", command=self.save_configuration)
        file_menu.add_separator()
        file_menu.add_command(label="Export Data...", command=self.export_data)
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.root.quit)
        
        # Tools menu
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Run Simulation", command=self.run_simulation)
        tools_menu.add_command(label="Clear Results", command=self.clear_results)
        
        # Help menu
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_main_notebook(self):
        """Create main tab notebook"""
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create tabs
        self.simulation_tab = SimulationTab(self.notebook, self.config_manager, self)
        self.estimation_tab = EstimationTab(self.notebook, self)
        self.viz_tab = VisualizationTab(self.notebook, self.analysis_engine, self)
        self.pohang_tab = PohangTab(self.notebook, self)
        self.pohang_viz_tab = PohangVisualizationTab(self.notebook, self)

        # Add tabs to notebook
        self.notebook.add(self.simulation_tab.frame, text="Simulation")
        self.notebook.add(self.estimation_tab.frame, text="Estimation")
        self.notebook.add(self.viz_tab.frame, text="Results & Visualization")
        self.notebook.add(self.pohang_tab.frame, text="Pohang Dataset")
        self.notebook.add(self.pohang_viz_tab.frame, text="Pohang Report")
    
    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_bar, variable=self.progress_var)
        self.progress_bar.pack(side=tk.RIGHT, padx=5, pady=2, fill=tk.X, expand=True)
    
    def update_status(self, message: str):
        """Update status bar message"""
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def update_progress(self, value: float):
        """Update progress bar (0-100)"""
        self.progress_var.set(value)
        self.root.update_idletasks()
    
    def new_configuration(self):
        """Create new configuration"""
        result = messagebox.askyesno("New Configuration", 
                                   "Create new configuration? Current settings will be lost.")
        if result:
            self.config_manager = ConfigurationManager()
            self.simulation_tab.refresh_ui()
            self.update_status("New configuration created")
    
    def open_configuration(self):
        """Open configuration file"""
        filename = filedialog.askopenfilename(
            title="Open Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            # Extract just the filename without extension
            config_name = os.path.splitext(os.path.basename(filename))[0]
            if self.config_manager.load_configuration(config_name):
                self.simulation_tab.refresh_ui()
                self.update_status(f"Configuration loaded: {config_name}")
            else:
                messagebox.showerror("Error", "Failed to load configuration")
    
    def save_configuration(self):
        """Save configuration file"""
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
            defaultextension=".json"
        )
        if filename:
            # Extract just the filename without extension
            config_name = os.path.splitext(os.path.basename(filename))[0]
            if self.config_manager.save_configuration(config_name):
                self.update_status(f"Configuration saved: {config_name}")
            else:
                messagebox.showerror("Error", "Failed to save configuration")
    
    def export_data(self):
        """Export simulation data"""
        if not hasattr(self, 'simulation_data') or self.simulation_data is None:
            messagebox.showwarning("No Data", "No simulation data to export. Run simulation first.")
            return
        
        filename = filedialog.asksaveasfilename(
            title="Export Data",
            filetypes=[("CSV files", "*.csv"), ("MAT files", "*.mat"), ("HDF5 files", "*.h5")],
            defaultextension=".csv"
        )
        if filename:
            try:
                self.analysis_engine.export_data(self.simulation_data, filename)
                self.update_status(f"Data exported: {filename}")
            except Exception as e:
                messagebox.showerror("Export Error", f"Failed to export data:\n{str(e)}")
    
    def run_simulation(self):
        """Run complete simulation and analysis"""
        try:
            self.update_status("Starting simulation...")
            self.update_progress(0)
            
            # Get current configuration
            traj_config = self.config_manager.get_trajectory_config()
            sensor_config = self.config_manager.get_sensor_config()
            env_config = self.config_manager.get_environment_config()
            
            # Run simulation
            self.update_progress(20)
            self.simulation_data = self.simulation_engine.run_simulation(
                traj_config, sensor_config, env_config
            )
            
            # Run fusion
            self.update_progress(50)
            self.estimation_results = self.fusion_engine.run_fusion(
                self.simulation_data, sensor_config
            )
            
            # Run analysis
            self.update_progress(80)
            self.analysis_results = self.analysis_engine.run_analysis(
                self.simulation_data, self.estimation_results
            )
            
            # Update visualization tab
            self.update_progress(90)
            self.viz_tab.update_results(self.analysis_results)
            
            # Switch to visualization tab
            self.notebook.select(2)  # Visualization is tab 2
            
            self.update_progress(100)
            self.update_status("Simulation completed successfully")
            
        except Exception as e:
            logger.error(f"Simulation failed: {e}")
            messagebox.showerror("Simulation Error", f"Simulation failed:\n{str(e)}")
            self.update_status("Simulation failed")
            self.update_progress(0)
    
    def clear_results(self):
        """Clear all simulation results"""
        self.simulation_data = None
        self.estimation_results = None
        self.analysis_results = None
        self.viz_tab.clear_results()
        self.update_status("Results cleared")
        self.update_progress(0)
    
    def show_about(self):
        """Show about dialog"""
        about_text = """INS/GPS Fusion Testing Platform
        
Production-ready testing platform for boat motion compensation evaluation.

Features:
• 7 trajectory types with configurable parameters
• Complete sensor simulation with selective fusion
• Integration with 28-state Extended Kalman Filter
• Comprehensive 2D/3D visualization
• Statistical analysis and error computation
• Data export in multiple formats

Developed for marine navigation and motion compensation applications.
"""
        messagebox.showinfo("About", about_text)
