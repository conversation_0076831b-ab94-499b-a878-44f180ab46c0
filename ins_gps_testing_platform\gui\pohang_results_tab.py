"""Pohang Results Viewer Tab - Display Saved Processing Results"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg, NavigationToolbar2Tk
import numpy as np
import pandas as pd
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class PohangVisualizationTab:
    """Pohang results viewer tab - displays saved processing results with interactive plots"""
    
    def __init__(self, parent_notebook, main_gui):
        self.parent_notebook = parent_notebook
        self.main_gui = main_gui
        
        # Create main frame
        self.frame = ttk.Frame(parent_notebook)
        
        # Data storage for loaded results
        self.trajectory_data = None
        self.gps_data = None
        self.error_data = None
        self.coordinate_data = None
        self.results_loaded = False
        
        # Results directory
        self.results_dir = Path("../exports/pohang_results")
        
        self.create_widgets()
        self.auto_load_latest_results()
        
    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Create main container with paned window for resizable sections
        main_paned = ttk.PanedWindow(self.frame, orient=tk.VERTICAL)
        main_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Top section: Controls
        self.create_control_section(main_paned)
        
        # Bottom section: Plots
        self.create_plot_section(main_paned)
        
        logger.info("Pohang Results Viewer Tab initialized")
    
    def create_control_section(self, parent):
        """Create control panel section"""
        control_frame = ttk.Frame(parent)
        parent.add(control_frame, weight=1)
        
        # Results Loading Section
        load_frame = ttk.LabelFrame(control_frame, text="Pohang Results Viewer", padding=10)
        load_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Results directory info
        info_frame = ttk.Frame(load_frame)
        info_frame.pack(fill=tk.X, pady=2)
        
        ttk.Label(info_frame, text="Results Directory:").pack(side=tk.LEFT)
        self.results_path_label = ttk.Label(info_frame, text=str(self.results_dir), 
                                          foreground="blue")
        self.results_path_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # Control buttons
        button_frame = ttk.Frame(load_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        self.refresh_button = ttk.Button(button_frame, text="Refresh Results", 
                                       command=self.refresh_results)
        self.refresh_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Clear Plots", 
                  command=self.clear_plots).pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Browse Directory", 
                  command=self.browse_results_directory).pack(side=tk.LEFT, padx=5)
        
        # Status
        status_frame = ttk.LabelFrame(control_frame, text="Status", padding=10)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_label = ttk.Label(status_frame, text="Ready to load Pohang results")
        self.status_label.pack(anchor=tk.W)
        
        # Results info
        self.results_info_label = ttk.Label(status_frame, text="No results loaded")
        self.results_info_label.pack(anchor=tk.W, pady=(5, 0))
    
    def auto_load_latest_results(self):
        """Automatically load the latest results on initialization"""
        try:
            self.load_csv_results()
            if self.results_loaded:
                self.update_all_plots()
                self.update_status_label("Latest results loaded successfully")
            else:
                self.update_status_label("No results found - run Pohang processing first")
        except Exception as e:
            self.update_status_label(f"Failed to load results: {e}")
            logger.error(f"Auto-load error: {e}")
    
    def refresh_results(self):
        """Refresh and reload the latest results"""
        try:
            self.clear_plots()
            self.load_csv_results()
            if self.results_loaded:
                self.update_all_plots()
                self.update_status_label("Results refreshed successfully")
            else:
                self.update_status_label("No results found - run Pohang processing first")
        except Exception as e:
            self.update_status_label(f"Failed to refresh results: {e}")
            logger.error(f"Refresh error: {e}")
    
    def browse_results_directory(self):
        """Browse for results directory"""
        directory = filedialog.askdirectory(
            title="Select Pohang Results Directory",
            initialdir=str(self.results_dir)
        )
        if directory:
            self.results_dir = Path(directory)
            self.results_path_label.config(text=str(self.results_dir))
            self.refresh_results()
    
    def load_csv_results(self):
        """Load CSV results from the results directory"""
        try:
            # Check if results directory exists
            if not self.results_dir.exists():
                self.results_loaded = False
                return
            
            # Load trajectory data
            trajectory_file = self.results_dir / "trajectory_data.csv"
            if trajectory_file.exists():
                self.trajectory_data = pd.read_csv(trajectory_file)
                logger.info(f"Loaded trajectory data: {len(self.trajectory_data)} points")
            else:
                self.trajectory_data = None

            # Load GPS data
            gps_file = self.results_dir / "gps_data.csv"
            if gps_file.exists():
                self.gps_data = pd.read_csv(gps_file)
                logger.info(f"Loaded GPS data: {len(self.gps_data)} points")
            else:
                self.gps_data = None
            
            # Load error metrics
            error_file = self.results_dir / "error_metrics.csv"
            if error_file.exists():
                self.error_data = pd.read_csv(error_file)
                logger.info(f"Loaded error data: {len(self.error_data)} points")
            else:
                self.error_data = None
            
            # Load coordinate frame data
            coord_file = self.results_dir / "coordinate_frames.csv"
            if coord_file.exists():
                self.coordinate_data = pd.read_csv(coord_file)
                logger.info(f"Loaded coordinate data: {len(self.coordinate_data)} frames")
            else:
                self.coordinate_data = None
            
            # Check if any data was loaded
            self.results_loaded = (self.trajectory_data is not None or 
                                 self.error_data is not None or 
                                 self.coordinate_data is not None)
            
            # Update results info
            if self.results_loaded:
                info_parts = []
                if self.trajectory_data is not None:
                    info_parts.append(f"Trajectory: {len(self.trajectory_data)} points")
                if self.error_data is not None:
                    info_parts.append(f"Errors: {len(self.error_data)} points")
                if self.coordinate_data is not None:
                    info_parts.append(f"Coordinates: {len(self.coordinate_data)} frames")
                
                self.results_info_label.config(text=" | ".join(info_parts))
            else:
                self.results_info_label.config(text="No CSV results found")
                
        except Exception as e:
            logger.error(f"Error loading CSV results: {e}")
            self.results_loaded = False
            raise
    
    def update_status_label(self, message):
        """Update status label"""
        self.status_label.config(text=message)
        logger.info(message)

    def create_plot_section(self, parent):
        """Create plot section with embedded matplotlib"""
        plot_frame = ttk.Frame(parent)
        parent.add(plot_frame, weight=4)

        # Create notebook for different plot categories
        self.plot_notebook = ttk.Notebook(plot_frame)
        self.plot_notebook.pack(fill=tk.BOTH, expand=True)

        # Create plot tabs
        self.create_trajectory_tab()
        self.create_error_analysis_tab()
        self.create_coordinate_frame_tab()

    def create_trajectory_tab(self):
        """Create trajectory comparison tab"""
        traj_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(traj_frame, text="Trajectory Comparison")

        # Create matplotlib figure
        self.traj_fig, self.traj_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.traj_fig.suptitle("Pohang Dataset - Trajectory Analysis")

        # 2D trajectory comparison
        self.traj_axes[0, 0].set_title("2D Trajectory Comparison (Local Coordinates)")
        self.traj_axes[0, 0].set_xlabel("Local East (m)")
        self.traj_axes[0, 0].set_ylabel("Local North (m)")
        self.traj_axes[0, 0].grid(True)
        self.traj_axes[0, 0].axis('equal')

        # 2D positioning error vs time
        self.traj_axes[0, 1].set_title("2D Positioning Error vs Relative Time (Local Coords)")
        self.traj_axes[0, 1].set_xlabel("Relative Time (s)")
        self.traj_axes[0, 1].set_ylabel("2D Position Error (m)")
        self.traj_axes[0, 1].grid(True)

        # Velocity comparison
        self.traj_axes[1, 0].set_title("Velocity Comparison")
        self.traj_axes[1, 0].set_xlabel("Time (s)")
        self.traj_axes[1, 0].set_ylabel("Velocity (m/s)")
        self.traj_axes[1, 0].grid(True)

        # Vertical positioning error comparison
        self.traj_axes[1, 1].set_title("Vertical Position Error vs Relative Time")
        self.traj_axes[1, 1].set_xlabel("Relative Time (s)")
        self.traj_axes[1, 1].set_ylabel("Vertical Position Error (m)")
        self.traj_axes[1, 1].grid(True)

        plt.tight_layout()

        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(traj_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.traj_canvas = FigureCanvasTkAgg(self.traj_fig, traj_frame)
        self.traj_canvas.draw()
        self.traj_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.traj_toolbar = NavigationToolbar2Tk(self.traj_canvas, toolbar_frame)
        self.traj_toolbar.update()

    def create_error_analysis_tab(self):
        """Create error analysis tab"""
        error_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(error_frame, text="Error Analysis")

        # Create matplotlib figure
        self.error_fig, self.error_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.error_fig.suptitle("Pohang Dataset - Error Analysis")

        # Position error vs time
        self.error_axes[0, 0].set_title("Position Error vs Time")
        self.error_axes[0, 0].set_xlabel("Time (s)")
        self.error_axes[0, 0].set_ylabel("Position Error (m)")
        self.error_axes[0, 0].grid(True)

        # Error distribution
        self.error_axes[0, 1].set_title("Position Error Distribution")
        self.error_axes[0, 1].set_xlabel("Position Error (m)")
        self.error_axes[0, 1].set_ylabel("Frequency")
        self.error_axes[0, 1].grid(True)

        # Error components
        self.error_axes[1, 0].set_title("Error Components vs Time")
        self.error_axes[1, 0].set_xlabel("Time (s)")
        self.error_axes[1, 0].set_ylabel("Error (m)")
        self.error_axes[1, 0].grid(True)

        # Error statistics
        self.error_axes[1, 1].set_title("Error Statistics")
        self.error_axes[1, 1].set_xlabel("Metric")
        self.error_axes[1, 1].set_ylabel("Error (m)")
        self.error_axes[1, 1].grid(True)

        plt.tight_layout()

        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(error_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.error_canvas = FigureCanvasTkAgg(self.error_fig, error_frame)
        self.error_canvas.draw()
        self.error_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.error_toolbar = NavigationToolbar2Tk(self.error_canvas, toolbar_frame)
        self.error_toolbar.update()

    def create_coordinate_frame_tab(self):
        """Create coordinate frame analysis tab"""
        coord_frame = ttk.Frame(self.plot_notebook)
        self.plot_notebook.add(coord_frame, text="Coordinate Frames")

        # Create matplotlib figure
        self.coord_fig, self.coord_axes = plt.subplots(2, 2, figsize=(12, 8))
        self.coord_fig.suptitle("Pohang Dataset - Coordinate Frame Analysis")

        # Coordinate ranges
        self.coord_axes[0, 0].set_title("Coordinate Frame Ranges")
        self.coord_axes[0, 0].set_xlabel("Frame Type")
        self.coord_axes[0, 0].set_ylabel("Range (m)")
        self.coord_axes[0, 0].grid(True)

        # Frame statistics
        self.coord_axes[0, 1].set_title("Frame Statistics")
        self.coord_axes[0, 1].set_xlabel("Parameter")
        self.coord_axes[0, 1].set_ylabel("Value")
        self.coord_axes[0, 1].grid(True)

        # Coordinate alignment
        self.coord_axes[1, 0].set_title("Coordinate Alignment")
        self.coord_axes[1, 0].set_xlabel("Original")
        self.coord_axes[1, 0].set_ylabel("Aligned")
        self.coord_axes[1, 0].grid(True)

        # Transformation info
        self.coord_axes[1, 1].set_title("Transformation Info")
        self.coord_axes[1, 1].set_xlabel("Component")
        self.coord_axes[1, 1].set_ylabel("Translation (m)")
        self.coord_axes[1, 1].grid(True)

        plt.tight_layout()

        # Create toolbar frame at the top first
        toolbar_frame = ttk.Frame(coord_frame)
        toolbar_frame.pack(side=tk.TOP, fill=tk.X)

        # Embed canvas in tkinter
        self.coord_canvas = FigureCanvasTkAgg(self.coord_fig, coord_frame)
        self.coord_canvas.draw()
        self.coord_canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Add toolbar to the top frame
        self.coord_toolbar = NavigationToolbar2Tk(self.coord_canvas, toolbar_frame)
        self.coord_toolbar.update()

    def update_all_plots(self):
        """Update all plots with loaded data"""
        if not self.results_loaded:
            return

        try:
            # Update trajectory plots
            if self.trajectory_data is not None:
                self.update_trajectory_plots()

            # Update error plots
            if self.error_data is not None:
                self.update_error_plots()

            # Update coordinate frame plots
            if self.coordinate_data is not None:
                self.update_coordinate_plots()

            # Redraw all canvases
            self.traj_canvas.draw()
            self.error_canvas.draw()
            self.coord_canvas.draw()

        except Exception as e:
            logger.error(f"Error updating plots: {e}")
            self.update_status_label(f"Plot update failed: {e}")

    def update_trajectory_plots(self):
        """Update trajectory comparison plots"""
        if self.trajectory_data is None:
            return

        try:
            # Clear all trajectory axes
            for ax in self.traj_axes.flat:
                ax.clear()

            # 2D trajectory comparison - Convert to local coordinates for better visualization
            ax = self.traj_axes[0, 0]
            ax.set_title("2D Trajectory Comparison (Local Coordinates)")
            ax.set_xlabel("Local East (m)")
            ax.set_ylabel("Local North (m)")
            ax.grid(True)
            ax.axis('equal')

            # Convert trajectories to local coordinates for better visualization
            local_coords = self._convert_to_local_coordinates()

            # Plot GPS measurements (raw data) from separate GPS file
            if (self.gps_data is not None and 'gps_east' in self.gps_data.columns and
                'gps_north' in self.gps_data.columns and local_coords['gps_local'] is not None):
                gps_local = local_coords['gps_local']
                ax.scatter(gps_local[:, 1], gps_local[:, 0],  # [east, north]
                          c='red', s=15, alpha=0.6, label='GPS Measurements (Raw)', marker='o')

            # Plot estimated trajectory
            if local_coords['est_local'] is not None:
                est_local = local_coords['est_local']
                ax.plot(est_local[:, 1], est_local[:, 0],  # [east, north]
                       'b-', linewidth=2, label='Estimated (GPS/IMU Fusion)')

            # Plot baseline trajectory if available
            if local_coords['base_local'] is not None:
                base_local = local_coords['base_local']
                # Filter out NaN values for plotting
                valid_mask = ~(np.isnan(base_local[:, 0]) | np.isnan(base_local[:, 1]))
                if np.any(valid_mask):
                    base_local_valid = base_local[valid_mask]
                    ax.plot(base_local_valid[:, 1], base_local_valid[:, 0],  # [east, north]
                           'g--', linewidth=2, label='Ground Truth (Baseline)')

            # Add coordinate system info
            if local_coords['reference_point'] is not None:
                ref_point = local_coords['reference_point']
                ax.text(0.02, 0.98, f'Local Origin:\nN: {ref_point[0]:.1f}m\nE: {ref_point[1]:.1f}m',
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

            ax.legend()

            # 2D positioning error vs time (using relative timestamps and local coordinates)
            ax = self.traj_axes[0, 1]
            ax.set_title("2D Positioning Error vs Relative Time (Local Coords)")
            ax.set_xlabel("Relative Time (s)")  # Updated to reflect relative time
            ax.set_ylabel("2D Position Error (m)")
            ax.grid(True)

            # Calculate and plot 2D positioning error using local coordinates
            if ('time' in self.trajectory_data.columns and
                'est_north' in self.trajectory_data.columns and
                'base_north' in self.trajectory_data.columns):

                # Get local coordinates for error calculation
                local_coords = self._convert_to_local_coordinates()

                if (local_coords['est_local'] is not None and
                    local_coords['base_local'] is not None):
                    # Calculate position errors in local coordinates
                    est_local = local_coords['est_local']
                    base_local = local_coords['base_local']

                    # Ensure same length for error calculation
                    min_len = min(len(est_local), len(base_local))
                    est_local = est_local[:min_len]
                    base_local = base_local[:min_len]

                    # Calculate errors, handling NaN values
                    error_north = est_local[:, 0] - base_local[:, 0]  # Local north error
                    error_east = est_local[:, 1] - base_local[:, 1]   # Local east error

                    # Calculate 2D (horizontal) positioning error
                    error_2d = np.sqrt(error_north**2 + error_east**2)

                    # Filter out NaN values for plotting and statistics
                    valid_mask = ~(np.isnan(error_north) | np.isnan(error_east))
                    if np.any(valid_mask):
                        error_2d = error_2d[valid_mask]
                        time_valid = self.trajectory_data['time'].values[:min_len][valid_mask]
                    else:
                        error_2d = np.array([])
                        time_valid = np.array([])
                else:
                    # Fallback to original coordinates if local conversion fails
                    error_north = self.trajectory_data['est_north'] - self.trajectory_data['base_north']
                    error_east = self.trajectory_data['est_east'] - self.trajectory_data['base_east']
                    error_2d = np.sqrt(error_north**2 + error_east**2)

                # Plot 2D error (only if we have valid data)
                if len(error_2d) > 0:
                    # Use filtered time data for plotting
                    plot_time = time_valid if 'time_valid' in locals() and len(time_valid) > 0 else self.trajectory_data['time'].values[:len(error_2d)]

                    ax.plot(plot_time, error_2d, 'r-', linewidth=2,
                           label='2D Position Error')

                    # Add RMS error as horizontal line
                    rms_error = np.sqrt(np.mean(error_2d**2))
                    ax.axhline(y=rms_error, color='orange', linestyle='--', linewidth=2,
                              label=f'RMS Error: {rms_error:.2f}m')

                    # Add statistics text
                    max_error = np.max(error_2d)
                    mean_error = np.mean(error_2d)
                    ax.text(0.02, 0.98, f'Max: {max_error:.2f}m\nMean: {mean_error:.2f}m\nValid: {len(error_2d)} pts',
                           transform=ax.transAxes, verticalalignment='top',
                           bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

                    ax.legend()
                else:
                    ax.text(0.5, 0.5, 'No valid error data available\n(baseline data may be sparse)',
                           transform=ax.transAxes, ha='center', va='center',
                           bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
            else:
                ax.text(0.5, 0.5, 'No trajectory data available\nfor error calculation',
                       transform=ax.transAxes, ha='center', va='center',
                       bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

            # Speed comparison (if velocity data available)
            ax = self.traj_axes[1, 0]
            ax.set_title("Velocity Comparison")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Velocity (m/s)")
            ax.grid(True)

            if 'time' in self.trajectory_data.columns:
                if 'est_vel_north' in self.trajectory_data.columns:
                    speed_est = np.sqrt(self.trajectory_data['est_vel_north']**2 +
                                      self.trajectory_data['est_vel_east']**2)
                    ax.plot(self.trajectory_data['time'], speed_est, 'b-', label='Estimated Speed')
                ax.legend()

            # Vertical position error vs time (using relative timestamps, same approach as 2D positioning error)
            ax = self.traj_axes[1, 1]
            ax.set_title("Vertical Position Error vs Relative Time")
            ax.set_xlabel("Relative Time (s)")  # Updated to reflect relative time
            ax.set_ylabel("Vertical Position Error (m)")
            ax.grid(True)

            # Calculate and plot vertical position error
            # IMPORTANT: Only use trajectory comparison error data for vertical errors
            # because trajectory data may have coordinate system reference issues
            if (self.error_data is not None and
                'time' in self.error_data.columns and
                'position_error_down' in self.error_data.columns):

                # Use error data directly from trajectory comparison (most accurate)
                # This data comes from SVD alignment which handles coordinate system differences
                down_error = self.error_data['position_error_down']
                time_data = self.error_data['time']
                data_source = "trajectory comparison (SVD aligned)"

            else:
                # Do NOT use trajectory data for vertical error calculation
                # because it may have coordinate system reference issues
                ax.text(0.5, 0.5, 'Vertical position error requires\ntrajectory comparison data\n\n' +
                       'Trajectory data may have\ncoordinate reference issues\nfor vertical coordinates',
                       transform=ax.transAxes, ha='center', va='center',
                       bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.8))
                down_error = None

            if down_error is not None:
                # Plot down error (vertical positioning error)
                # Positive down error means estimated trajectory is below baseline
                # Negative down error means estimated trajectory is above baseline
                ax.plot(time_data, down_error, 'r-', linewidth=2,
                       label='Vertical Position Error')

                # Add RMS error as horizontal line
                rms_error = np.sqrt(np.mean(down_error**2))
                ax.axhline(y=rms_error, color='orange', linestyle='--', linewidth=2,
                          label=f'RMS Error: {rms_error:.2f}m')
                ax.axhline(y=-rms_error, color='orange', linestyle='--', linewidth=2)
                ax.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.3)

                # Add statistics text
                mean_error = np.mean(down_error)
                std_error = np.std(down_error)
                max_error = np.max(np.abs(down_error))
                ax.text(0.02, 0.98, f'Mean: {mean_error:.2f}m\nStd: {std_error:.2f}m\nMax: {max_error:.2f}m\nSource: {data_source}',
                       transform=ax.transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

                ax.legend()

        except Exception as e:
            logger.error(f"Error updating trajectory plots: {e}")

    def _convert_to_local_coordinates(self):
        """
        Convert trajectory coordinates to local coordinate system for better visualization.
        Uses the centroid of the baseline trajectory as the local origin.

        Returns:
            Dictionary with local coordinate arrays and reference point
        """
        result = {
            'est_local': None,
            'base_local': None,
            'gps_local': None,
            'reference_point': None
        }

        try:
            # Determine reference point (local origin) from baseline trajectory centroid
            reference_point = None

            if ('base_east' in self.trajectory_data.columns and
                'base_north' in self.trajectory_data.columns):
                # Use baseline trajectory centroid as reference (excluding NaN values)
                base_north = self.trajectory_data['base_north'].values
                base_east = self.trajectory_data['base_east'].values

                # Filter out NaN values
                valid_mask = ~(np.isnan(base_north) | np.isnan(base_east))
                if np.any(valid_mask):
                    base_north_valid = base_north[valid_mask]
                    base_east_valid = base_east[valid_mask]
                    reference_point = np.array([np.mean(base_north_valid), np.mean(base_east_valid)])
                    logger.info(f"Using baseline centroid as local origin: N={reference_point[0]:.1f}, E={reference_point[1]:.1f}")
                    logger.info(f"Baseline data: {len(base_north_valid)} valid points out of {len(base_north)} total")

            if reference_point is None and ('est_east' in self.trajectory_data.columns and
                                          'est_north' in self.trajectory_data.columns):
                # Fallback to estimated trajectory centroid
                est_north = self.trajectory_data['est_north'].values
                est_east = self.trajectory_data['est_east'].values
                reference_point = np.array([np.mean(est_north), np.mean(est_east)])
                logger.info(f"Using estimated centroid as local origin: N={reference_point[0]:.1f}, E={reference_point[1]:.1f}")

            if reference_point is None:
                logger.warning("No trajectory data available for local coordinate conversion")
                return result

            result['reference_point'] = reference_point

            # Convert estimated trajectory to local coordinates
            if ('est_east' in self.trajectory_data.columns and
                'est_north' in self.trajectory_data.columns):
                est_north = self.trajectory_data['est_north'].values
                est_east = self.trajectory_data['est_east'].values
                est_down = self.trajectory_data.get('est_down', np.zeros_like(est_north)).values

                # Convert to local coordinates (subtract reference point)
                est_local_north = est_north - reference_point[0]
                est_local_east = est_east - reference_point[1]
                result['est_local'] = np.column_stack([est_local_north, est_local_east, est_down])

            # Convert baseline trajectory to local coordinates
            if ('base_east' in self.trajectory_data.columns and
                'base_north' in self.trajectory_data.columns):
                base_north = self.trajectory_data['base_north'].values
                base_east = self.trajectory_data['base_east'].values
                base_down = self.trajectory_data.get('base_down', np.zeros_like(base_north)).values

                # Convert to local coordinates (subtract reference point), handling NaN values
                base_local_north = base_north - reference_point[0]
                base_local_east = base_east - reference_point[1]
                result['base_local'] = np.column_stack([base_local_north, base_local_east, base_down])

            # Convert GPS data to local coordinates
            if (self.gps_data is not None and 'gps_east' in self.gps_data.columns and
                'gps_north' in self.gps_data.columns):
                gps_north = self.gps_data['gps_north'].values
                gps_east = self.gps_data['gps_east'].values
                gps_down = self.gps_data.get('gps_down', np.zeros_like(gps_north)).values

                # Convert to local coordinates (subtract reference point)
                gps_local_north = gps_north - reference_point[0]
                gps_local_east = gps_east - reference_point[1]
                result['gps_local'] = np.column_stack([gps_local_north, gps_local_east, gps_down])

        except Exception as e:
            logger.error(f"Error converting to local coordinates: {e}")

        return result

    def update_error_plots(self):
        """Update error analysis plots"""
        if self.error_data is None:
            return

        try:
            # Clear all error axes
            for ax in self.error_axes.flat:
                ax.clear()

            # Position error vs time (using relative timestamps from GPS data)
            ax = self.error_axes[0, 0]
            ax.set_title("Position Error vs Relative Time")
            ax.set_xlabel("Relative Time (s)")  # Updated to reflect relative time
            ax.set_ylabel("Position Error (m)")
            ax.grid(True)

            if 'time' in self.error_data.columns and 'position_error_3d' in self.error_data.columns:
                # Note: 'time' column now contains relative timestamps (starting from 0)
                # corresponding to GPS data points and their co-located baseline points
                ax.plot(self.error_data['time'], self.error_data['position_error_3d'],
                       'r-', linewidth=1, label='3D Position Error')
                ax.legend()

            # Error distribution
            ax = self.error_axes[0, 1]
            ax.set_title("Position Error Distribution")
            ax.set_xlabel("Position Error (m)")
            ax.set_ylabel("Frequency")
            ax.grid(True)

            if 'position_error_3d' in self.error_data.columns:
                ax.hist(self.error_data['position_error_3d'], bins=30, alpha=0.7, color='red')

            # Component errors
            ax = self.error_axes[1, 0]
            ax.set_title("Error Components vs Time")
            ax.set_xlabel("Time (s)")
            ax.set_ylabel("Error (m)")
            ax.grid(True)

            if 'time' in self.error_data.columns:
                if 'position_error_north' in self.error_data.columns:
                    ax.plot(self.error_data['time'], self.error_data['position_error_north'],
                           'b-', label='North Error')
                if 'position_error_east' in self.error_data.columns:
                    ax.plot(self.error_data['time'], self.error_data['position_error_east'],
                           'r-', label='East Error')
                if 'position_error_down' in self.error_data.columns:
                    ax.plot(self.error_data['time'], self.error_data['position_error_down'],
                           'g-', label='Down Error')
                ax.legend()

            # Error statistics
            ax = self.error_axes[1, 1]
            ax.set_title("Error Statistics")
            ax.set_xlabel("Metric")
            ax.set_ylabel("Error (m)")
            ax.grid(True)

            # Calculate basic statistics
            if 'position_error_3d' in self.error_data.columns:
                errors = self.error_data['position_error_3d']
                stats = ['RMS', 'Mean', 'Max', 'Std']
                values = [np.sqrt(np.mean(errors**2)), np.mean(errors),
                         np.max(errors), np.std(errors)]
                ax.bar(stats, values, alpha=0.7, color=['blue', 'green', 'red', 'orange'])

        except Exception as e:
            logger.error(f"Error updating error plots: {e}")

    def update_coordinate_plots(self):
        """Update coordinate frame plots"""
        if self.coordinate_data is None:
            return

        try:
            # Clear all coordinate axes
            for ax in self.coord_axes.flat:
                ax.clear()

            # Coordinate ranges comparison
            ax = self.coord_axes[0, 0]
            ax.set_title("Coordinate Frame Ranges")
            ax.set_xlabel("Frame Type")
            ax.set_ylabel("Range (m)")
            ax.grid(True)

            if 'frame_type' in self.coordinate_data.columns:
                frames = self.coordinate_data['frame_type'].unique()
                for i, frame in enumerate(frames):
                    frame_data = self.coordinate_data[self.coordinate_data['frame_type'] == frame]
                    if len(frame_data) > 0:
                        north_range = frame_data['north_max'].iloc[0] - frame_data['north_min'].iloc[0]
                        east_range = frame_data['east_max'].iloc[0] - frame_data['east_min'].iloc[0]
                        ax.bar([f'{frame}_N', f'{frame}_E'], [north_range, east_range],
                              alpha=0.7, label=frame)
                ax.legend()

        except Exception as e:
            logger.error(f"Error updating coordinate plots: {e}")

    def clear_plots(self):
        """Clear all plots"""
        try:
            # Clear trajectory plots
            for ax in self.traj_axes.flat:
                ax.clear()
                ax.grid(True)

            # Clear error plots
            for ax in self.error_axes.flat:
                ax.clear()
                ax.grid(True)

            # Clear coordinate plots
            for ax in self.coord_axes.flat:
                ax.clear()
                ax.grid(True)

            # Reset titles
            self.reset_plot_titles()

            # Redraw canvases
            self.traj_canvas.draw()
            self.error_canvas.draw()
            self.coord_canvas.draw()

        except Exception as e:
            logger.error(f"Error clearing plots: {e}")

    def reset_plot_titles(self):
        """Reset plot titles after clearing"""
        # Trajectory tab titles
        self.traj_axes[0, 0].set_title("2D Trajectory Comparison (Local Coordinates)")
        self.traj_axes[0, 0].set_xlabel("Local East (m)")
        self.traj_axes[0, 0].set_ylabel("Local North (m)")

        self.traj_axes[0, 1].set_title("2D Positioning Error vs Relative Time (Local Coords)")
        self.traj_axes[0, 1].set_xlabel("Relative Time (s)")
        self.traj_axes[0, 1].set_ylabel("2D Position Error (m)")

        self.traj_axes[1, 0].set_title("Velocity Comparison")
        self.traj_axes[1, 0].set_xlabel("Time (s)")
        self.traj_axes[1, 0].set_ylabel("Velocity (m/s)")

        self.traj_axes[1, 1].set_title("Vertical Position Error vs Relative Time")
        self.traj_axes[1, 1].set_xlabel("Relative Time (s)")
        self.traj_axes[1, 1].set_ylabel("Vertical Position Error (m)")

        # Error tab titles
        self.error_axes[0, 0].set_title("Position Error vs Time")
        self.error_axes[0, 0].set_xlabel("Time (s)")
        self.error_axes[0, 0].set_ylabel("Position Error (m)")

        self.error_axes[0, 1].set_title("Position Error Distribution")
        self.error_axes[0, 1].set_xlabel("Position Error (m)")
        self.error_axes[0, 1].set_ylabel("Frequency")

        self.error_axes[1, 0].set_title("Error Components vs Time")
        self.error_axes[1, 0].set_xlabel("Time (s)")
        self.error_axes[1, 0].set_ylabel("Error (m)")

        self.error_axes[1, 1].set_title("Error Statistics")
        self.error_axes[1, 1].set_xlabel("Metric")
        self.error_axes[1, 1].set_ylabel("Error (m)")

        # Coordinate tab titles
        self.coord_axes[0, 0].set_title("Coordinate Frame Ranges")
        self.coord_axes[0, 0].set_xlabel("Frame Type")
        self.coord_axes[0, 0].set_ylabel("Range (m)")

        self.coord_axes[0, 1].set_title("Frame Statistics")
        self.coord_axes[0, 1].set_xlabel("Parameter")
        self.coord_axes[0, 1].set_ylabel("Value")

        self.coord_axes[1, 0].set_title("Coordinate Alignment")
        self.coord_axes[1, 0].set_xlabel("Original")
        self.coord_axes[1, 0].set_ylabel("Aligned")

        self.coord_axes[1, 1].set_title("Transformation Info")
        self.coord_axes[1, 1].set_xlabel("Component")
        self.coord_axes[1, 1].set_ylabel("Translation (m)")
