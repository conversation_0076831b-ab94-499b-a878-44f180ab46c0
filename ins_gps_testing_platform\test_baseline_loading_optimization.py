#!/usr/bin/env python3
"""
Test script to verify baseline data loading optimization.

This script tests that baseline data is loaded only once and cached for reuse,
eliminating redundant file I/O operations.
"""

import numpy as np
import time
import logging
from pathlib import Path
import sys
from unittest.mock import patch, MagicMock

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_baseline_loading_optimization():
    """Test that baseline data is loaded only once and cached"""
    
    logger.info("="*60)
    logger.info("TESTING BASELINE DATA LOADING OPTIMIZATION")
    logger.info("="*60)
    
    # Mock the PohangDatasetLoader to avoid needing real data files
    from pohang.pohang_analizor import PohangDatasetLoader, PohangDatasetConfig
    
    # Create a mock dataset path
    mock_dataset_path = Path("/mock/dataset/path")
    
    # Create synthetic baseline data for testing
    synthetic_baseline_data = np.array([
        [1000.0, 1.0, 0.0, 0.0, 0.0, 100.0, 200.0, -5.0],  # time, quat, position
        [1001.0, 1.0, 0.0, 0.0, 0.0, 101.0, 201.0, -5.1],
        [1002.0, 1.0, 0.0, 0.0, 0.0, 102.0, 202.0, -5.2],
        [1003.0, 1.0, 0.0, 0.0, 0.0, 103.0, 203.0, -5.3],
        [1004.0, 1.0, 0.0, 0.0, 0.0, 104.0, 204.0, -5.4],
    ])
    
    # Track how many times np.loadtxt is called
    loadtxt_call_count = 0
    original_loadtxt = np.loadtxt
    
    def mock_loadtxt(filepath, *args, **kwargs):
        nonlocal loadtxt_call_count
        loadtxt_call_count += 1
        logger.info(f"📁 np.loadtxt called #{loadtxt_call_count} for: {filepath}")
        return synthetic_baseline_data
    
    # Mock file existence check
    def mock_exists(self):
        return str(self).endswith("baseline.txt")
    
    try:
        # Apply mocks
        with patch('numpy.loadtxt', side_effect=mock_loadtxt), \
             patch.object(Path, 'exists', mock_exists):
            
            # Create loader instance
            config = PohangDatasetConfig()
            loader = PohangDatasetLoader(mock_dataset_path, config)
            
            logger.info("\n1. TESTING MULTIPLE BASELINE LOADING CALLS:")
            
            # Test 1: Call load_baseline_data multiple times
            logger.info("   Calling load_baseline_data() first time...")
            result1 = loader.load_baseline_data()
            first_call_count = loadtxt_call_count
            
            logger.info("   Calling load_baseline_data() second time...")
            result2 = loader.load_baseline_data()
            second_call_count = loadtxt_call_count
            
            logger.info("   Calling load_baseline_data() third time...")
            result3 = loader.load_baseline_data()
            third_call_count = loadtxt_call_count
            
            # Test 2: Call functions that use baseline data
            logger.info("\n2. TESTING FUNCTIONS THAT REUSE BASELINE DATA:")
            
            logger.info("   Calling _set_reference_from_baseline()...")
            loader._set_reference_from_baseline()
            after_reference_count = loadtxt_call_count
            
            logger.info("   Calling _correlate_baseline_origin_with_gps()...")
            # Mock GPS data for correlation
            gps_lats = np.array([35.9, 35.91, 35.92])
            gps_lons = np.array([129.36, 129.37, 129.38])
            gps_alts = np.array([0.0, 0.1, 0.2])
            loader._correlate_baseline_origin_with_gps()
            after_correlation_count = loadtxt_call_count
            
            # Test 3: Verify data consistency
            logger.info("\n3. TESTING DATA CONSISTENCY:")
            
            # All results should be the same object (cached)
            data_consistent = (result1 is result2 is result3)
            logger.info(f"   Data objects are identical (cached): {data_consistent}")
            
            # Verify data content
            if result1 is not None:
                logger.info(f"   Baseline data points: {len(result1['time'])}")
                logger.info(f"   Time range: {result1['time'][0]:.1f} - {result1['time'][-1]:.1f}")
                logger.info(f"   Position shape: {result1['position'].shape}")
            
            # Results
            logger.info("\n4. OPTIMIZATION RESULTS:")
            logger.info(f"   Total np.loadtxt calls: {loadtxt_call_count}")
            logger.info(f"   After 1st load_baseline_data(): {first_call_count}")
            logger.info(f"   After 2nd load_baseline_data(): {second_call_count}")
            logger.info(f"   After 3rd load_baseline_data(): {third_call_count}")
            logger.info(f"   After _set_reference_from_baseline(): {after_reference_count}")
            logger.info(f"   After _correlate_baseline_origin_with_gps(): {after_correlation_count}")
            
            # Check optimization success
            optimization_success = (
                loadtxt_call_count == 1 and  # Only one file load
                data_consistent and          # Data is cached
                result1 is not None          # Data was loaded successfully
            )
            
            logger.info("\n5. OPTIMIZATION ASSESSMENT:")
            if optimization_success:
                logger.info("   ✅ OPTIMIZATION SUCCESSFUL!")
                logger.info("   ✅ Baseline data loaded only once")
                logger.info("   ✅ Data is properly cached and reused")
                logger.info("   ✅ No redundant file I/O operations")
            else:
                logger.info("   ❌ OPTIMIZATION FAILED!")
                if loadtxt_call_count > 1:
                    logger.info(f"   ❌ File loaded {loadtxt_call_count} times (should be 1)")
                if not data_consistent:
                    logger.info("   ❌ Data not properly cached")
                if result1 is None:
                    logger.info("   ❌ Data loading failed")
            
            return optimization_success
            
    except Exception as e:
        logger.error(f"Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_performance_improvement():
    """Test performance improvement from caching"""
    
    logger.info("\n" + "="*60)
    logger.info("TESTING PERFORMANCE IMPROVEMENT")
    logger.info("="*60)
    
    # Simulate file loading time
    def slow_loadtxt(filepath, *args, **kwargs):
        time.sleep(0.1)  # Simulate 100ms file I/O
        return np.random.randn(1000, 8)  # Large dataset
    
    def mock_exists(self):
        return str(self).endswith("baseline.txt")
    
    try:
        with patch('numpy.loadtxt', side_effect=slow_loadtxt), \
             patch.object(Path, 'exists', mock_exists):
            
            from pohang.pohang_analizor import PohangDatasetLoader, PohangDatasetConfig
            
            config = PohangDatasetConfig()
            loader = PohangDatasetLoader(Path("/mock/path"), config)
            
            # Test without optimization (multiple loads)
            logger.info("Simulating OLD behavior (multiple file loads):")
            start_time = time.time()
            
            # Simulate calling load multiple times (old behavior)
            for i in range(5):
                slow_loadtxt("baseline.txt")  # Direct call to simulate old behavior
            
            old_time = time.time() - start_time
            logger.info(f"   Time for 5 separate loads: {old_time:.3f}s")
            
            # Test with optimization (cached loading)
            logger.info("Testing NEW behavior (cached loading):")
            start_time = time.time()
            
            # First load
            loader.load_baseline_data()
            # Subsequent calls use cache
            for i in range(4):
                loader.load_baseline_data()
            
            new_time = time.time() - start_time
            logger.info(f"   Time for 1 load + 4 cache hits: {new_time:.3f}s")
            
            # Calculate improvement
            improvement = ((old_time - new_time) / old_time) * 100
            logger.info(f"\nPerformance improvement: {improvement:.1f}%")
            
            if improvement > 50:
                logger.info("   ✅ SIGNIFICANT PERFORMANCE IMPROVEMENT!")
            else:
                logger.info("   ⚠️  Performance improvement less than expected")
            
            return improvement > 50
            
    except Exception as e:
        logger.error(f"Performance test failed: {e}")
        return False

def main():
    """Main test function"""
    try:
        # Test optimization
        optimization_success = test_baseline_loading_optimization()
        
        # Test performance
        performance_success = test_performance_improvement()
        
        # Final summary
        logger.info("\n" + "="*60)
        logger.info("BASELINE LOADING OPTIMIZATION TEST SUMMARY")
        logger.info("="*60)
        
        if optimization_success and performance_success:
            logger.info("🎯 SUCCESS: Baseline loading optimization is working!")
            logger.info("✅ Baseline data is loaded only once from baseline.txt")
            logger.info("✅ Data is properly cached and reused")
            logger.info("✅ Redundant file I/O operations eliminated")
            logger.info("✅ Significant performance improvement achieved")
        else:
            logger.info("⚠️  WARNING: Some optimization issues detected")
            if not optimization_success:
                logger.info("❌ Caching optimization failed")
            if not performance_success:
                logger.info("❌ Performance improvement insufficient")
        
        return optimization_success and performance_success
        
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
