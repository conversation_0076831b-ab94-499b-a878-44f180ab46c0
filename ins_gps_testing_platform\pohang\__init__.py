"""
Pohang Dataset Processing Module

This module provides tools for processing the Pohang Canal Dataset for INS/GPS fusion
and LiDAR georeferencing applications.

Components:
- PohangDatasetLoader: Load and preprocess Pohang dataset files
- LidarGeoreferencer: Georeference LiDAR point clouds using INS/GPS results
- TrajectoryComparator: Compare fusion results with baseline trajectories
- PohangLidarProcessor: Complete processing pipeline

Usage:
    from pohang import PohangLidarProcessor
    processor = PohangLidarProcessor(dataset_path)
    results = processor.run_ins_gps_fusion()
"""

import sys
from pathlib import Path
# Add parent directory to path for absolute imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from pohang.pohang_analizor import PohangDatasetLoader, PohangDatasetConfig
from pohang.lidar_georeferencing import LidarGeoreferencer, LidarExtrinsics
from pohang.trajectory_comparison import TrajectoryComparator
from pohang.pohang_integration import PohangLidarProcessor

__all__ = [
    'PohangDatasetLoader',
    'PohangDatasetConfig', 
    'LidarGeoreferencer',
    'TrajectoryComparator',
    'LidarExtrinsics',
    'PohangLidarProcessor'
]
