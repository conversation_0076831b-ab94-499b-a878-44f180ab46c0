
"""
Pohang Dataset Loader and Processor
Handles loading and preprocessing of Pohang Canal Dataset for INS/GPS fusion
"""

import numpy as np
import os
import json
import copy
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, Tuple, Optional
import logging
from scipy.spatial.transform import Rotation
from scipy.interpolate import interp1d

# Import your existing data structures
import sys
from pathlib import Path
# Add parent directory to path for absolute imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from simulation.simulation_engine import GroundTruth, SensorMeasurements, SimulationData
from config.configuration_manager import TrajectoryConfig, SensorConfig, EnvironmentConfig
from preprocessing.marine_imu_preprocessor import MarineIMUPreprocessor, PreprocessingConfig

logger = logging.getLogger(__name__)

@dataclass
class PohangDatasetConfig:
    """Configuration for Pohang dataset processing"""
    # Reference location (will be set from first GPS reading)
    ref_latitude: float = None  # degrees (will be set from first GPS reading)
    ref_longitude: float = None  # degrees (will be set from first GPS reading)
    ref_altitude: float = 0.0  # meters (sea level)

    # Earth model parameters for coordinate conversion
    earth_radius: float = 6378137.0  # meters (WGS84)
    earth_flattening: float = 1.0 / 298.257223563  # WGS84

    # Data synchronization parameters
    time_tolerance: float = 0.01  # seconds for time matching
    interpolation_method: str = 'linear'  # or 'nearest', 'cubic'

    # Processing time limits (for faster analysis)
    max_processing_duration: float = None  # seconds (None = process all data)
    start_time_offset: float = 0.0  # seconds from beginning to start processing
    
    # Sensor noise parameters (loaded from pohang_config.json)
    gps_horizontal_accuracy: float = None  # Will be loaded from config file
    gps_vertical_accuracy: float = None    # Will be loaded from config file
    imu_accelerometer_noise: float = None  # Will be loaded from config file
    imu_gyroscope_noise: float = None      # Will be loaded from config file
    
    # Dataset-specific parameters
    gravity_magnitude: float = 9.80665  # m/s²
    
class PohangDatasetLoader:
    """
    Loads and processes Pohang Canal Dataset for INS/GPS fusion
    
    Handles:
    - AHRS data (IMU measurements)
    - GPS data (position and heading)
    - Baseline data (ground truth)
    - Coordinate transformations
    - Data synchronization and packaging
    """
    
    def __init__(self, dataset_path: str, config: Optional[PohangDatasetConfig] = None):
        """
        Initialize dataset loader

        Args:
            dataset_path: Path to dataset directory containing navigation/ folder
            config: Dataset configuration parameters
        """
        self.dataset_path = Path(dataset_path)
        self.config = config or PohangDatasetConfig()

        # Validate dataset path
        if not self.dataset_path.exists():
            raise ValueError(f"Dataset path does not exist: {dataset_path}")

        # Load sensor parameters from pohang_config.json if not provided
        if (self.config.gps_horizontal_accuracy is None or
            self.config.gps_vertical_accuracy is None or
            self.config.imu_accelerometer_noise is None or
            self.config.imu_gyroscope_noise is None):
            self._load_sensor_parameters_from_config()

        # Set reference location from baseline data if available, otherwise GPS
        if self.config.ref_latitude is None or self.config.ref_longitude is None:
            if not self._set_reference_from_baseline():
                self._set_reference_from_first_gps()

        # Initialize coordinate transformation parameters
        self._init_coordinate_transforms()

        # Data storage
        self.ahrs_data = None
        self.gps_data = None
        self.baseline_data = None
        self.baseline_center_offset = None  # Store baseline center offset if in local coordinates

    def _load_sensor_parameters_from_config(self):
        """
        Load sensor parameters from pohang_config.json file
        """
        try:
            # Look for pohang_config.json in the pohang directory
            config_file = Path(__file__).parent / "pohnag_config.json"

            if not config_file.exists():
                logger.warning(f"Pohang config file not found: {config_file}")
                self._use_default_sensor_parameters()
                return

            with open(config_file, 'r') as f:
                config_data = json.load(f)

            # Extract sensor parameters
            sensors = config_data.get('sensors', {})
            gps_config = sensors.get('gps', {})
            imu_config = sensors.get('imu', {})

            # Load GPS parameters
            self.config.gps_horizontal_accuracy = gps_config.get('horizontal_accuracy', 2.0)
            self.config.gps_vertical_accuracy = gps_config.get('vertical_accuracy', 3.0)

            # Load IMU parameters
            self.config.imu_accelerometer_noise = imu_config.get('accelerometer_noise', 0.0004)
            self.config.imu_gyroscope_noise = imu_config.get('gyroscope_noise', 3.87e-5)

            logger.info(f"Loaded sensor parameters from {config_file}")
            logger.info(f"  GPS accuracy: H={self.config.gps_horizontal_accuracy}m, V={self.config.gps_vertical_accuracy}m")
            logger.info(f"  IMU noise: Accel={self.config.imu_accelerometer_noise:.2e} m/s², Gyro={self.config.imu_gyroscope_noise:.2e} rad/s")

        except Exception as e:
            logger.error(f"Error loading sensor parameters from config file: {e}")
            self._use_default_sensor_parameters()

    def _use_default_sensor_parameters(self):
        """
        Use default sensor parameters as fallback - should not be reached if config file exists
        """
        logger.error("Configuration file missing or invalid - cannot proceed without sensor parameters")
        raise ValueError("Pohang configuration file (pohnag_config.json) is required but not found or invalid. "
                        "Please ensure the configuration file exists and contains valid sensor parameters.")

    def set_processing_limits(self, max_duration: Optional[float] = None,
                             start_offset: float = 0.0):
        """
        Set time limits for processing to speed up analysis

        Args:
            max_duration: Maximum duration to process in seconds (None = process all)
            start_offset: Time offset from beginning to start processing (seconds)
        """
        self.config.max_processing_duration = max_duration
        self.config.start_time_offset = start_offset

        if max_duration is not None:
            logger.info(f"Set processing limits: {max_duration}s duration, "
                       f"{start_offset}s offset")
        else:
            logger.info("Removed processing time limits - will process all data")

    def _set_reference_from_baseline(self):
        """
        Extract LGF origin from the baseline coordinate system origin.
        The baseline coordinate system origin should be used as the LGF reference.
        """
        try:
            # Try to read baseline file to understand its coordinate system
            baseline_file = self.dataset_path / "navigation" / "baseline.txt"
            if not baseline_file.exists():
                baseline_file = self.dataset_path / "navigation" / "baseline_sampled.txt"

            if baseline_file.exists():
                logger.info(f"Analyzing baseline coordinate system: {baseline_file}")

                # Load baseline data to understand coordinate system
                data = np.loadtxt(baseline_file)

                if data.shape[1] >= 8:
                    # Columns 5,6,7 are position (x,y,z) in baseline coordinate system
                    positions = data[:, 5:8]  # [x, y, z] positions

                    # Analyze baseline coordinate system
                    pos_magnitudes = np.abs(positions).mean(axis=0)
                    pos_range = positions.max(axis=0) - positions.min(axis=0)
                    pos_min = positions.min(axis=0)

                    logger.info(f"Baseline coordinate analysis:")
                    logger.info(f"  Position range: [{pos_range[0]:.3f}, {pos_range[1]:.3f}, {pos_range[2]:.3f}]")
                    logger.info(f"  Position min: [{pos_min[0]:.3f}, {pos_min[1]:.3f}, {pos_min[2]:.3f}]")
                    logger.info(f"  Mean magnitude: [{pos_magnitudes[0]:.3f}, {pos_magnitudes[1]:.3f}, {pos_magnitudes[2]:.3f}]")

                    

                    # For now, we'll use the GPS data to find the reference location
                    # that corresponds to the baseline coordinate system origin
                    logger.info("Baseline coordinate system detected - will use GPS to find corresponding LGF origin")
                    return False  # Let GPS method handle it, but with baseline awareness

                else:
                    logger.warning("Baseline file format not recognized")
                    return False
            else:
                logger.info("No baseline file found")
                return False

        except Exception as e:
            logger.error(f"Error analyzing baseline coordinate system: {e}")
            return False

    def _set_reference_from_first_gps(self):
        """
        Set LGF reference location that corresponds to baseline coordinate system origin.
        Find GPS location that should map to baseline (0,0,0).
        """
        try:
            # Load GPS data
            gps_file = self.dataset_path / "navigation" / "gps.txt"
            if not gps_file.exists():
                gps_file = self.dataset_path / "navigation" / "gps_sampled.txt"

            if gps_file.exists():
                logger.info(f"Finding LGF origin corresponding to baseline coordinate system: {gps_file}")

                # Load GPS data
                data = np.loadtxt(gps_file, dtype=str, delimiter='\t')

                if data.shape[1] >= 11:
                    # Extract lat/lon data
                    latitudes = data[:, 2].astype(float)
                    longitudes = data[:, 4].astype(float)
                    altitudes = data[:, 10].astype(float)

                    # Apply hemisphere corrections
                    lat_hemispheres = data[:, 3]
                    lon_hemispheres = data[:, 5]

                    for i in range(len(latitudes)):
                        if lat_hemispheres[i] == 'S':
                            latitudes[i] *= -1
                        if lon_hemispheres[i] == 'W':
                            longitudes[i] *= -1

                    # Strategy: Find GPS location that corresponds to baseline coordinate system origin
                    # We need to correlate GPS and baseline data to find the mapping

                    # Try to load baseline data to understand the coordinate relationship
                    baseline_origin_gps = self._find_baseline_origin_gps(latitudes, longitudes, altitudes)

                    if baseline_origin_gps is not None:
                        ref_lat, ref_lon, ref_alt = baseline_origin_gps
                        logger.info("Found GPS location corresponding to baseline origin")
                    else:
                        # Fallback: use first GPS point as approximation
                        ref_lat = latitudes[0]
                        ref_lon = longitudes[0]
                        ref_alt = altitudes[0]
                        logger.warning("Could not correlate baseline origin with GPS - using first GPS point")

                    # Calculate trajectory span for information
                    lat_range = np.max(latitudes) - np.min(latitudes)
                    lon_range = np.max(longitudes) - np.min(longitudes)
                    alt_range = np.max(altitudes) - np.min(altitudes)

                    self.config.ref_latitude = ref_lat
                    self.config.ref_longitude = ref_lon
                    self.config.ref_altitude = ref_alt

                    logger.info(f"LGF origin set to correspond with baseline coordinate system:")
                    logger.info(f"  Latitude: {ref_lat:.6f}°")
                    logger.info(f"  Longitude: {ref_lon:.6f}°")
                    logger.info(f"  Altitude: {ref_alt:.3f}m")
                    logger.info(f"  GPS trajectory span: lat={lat_range:.6f}°, lon={lon_range:.6f}°, alt={alt_range:.3f}m")
                    logger.info("  This LGF origin should align with baseline coordinate system origin")

                else:
                    logger.warning("Could not parse GPS data, using default reference")
                    self._use_default_reference()
            else:
                logger.warning("No GPS file found, using default reference location")
                self._use_default_reference()

        except Exception as e:
            logger.error(f"Error finding LGF origin for baseline alignment: {e}")
            self._use_default_reference()

    def _use_default_reference(self):
        """Load reference location from configuration file"""
        try:
            config_file = Path(__file__).parent / "pohnag_config.json"
            with open(config_file, 'r') as f:
                config_data = json.load(f)

            location = config_data.get('dataset', {}).get('location', {})
            self.config.ref_latitude = location.get('latitude')
            self.config.ref_longitude = location.get('longitude')
            self.config.ref_altitude = location.get('altitude', 0.0)

            if self.config.ref_latitude is None or self.config.ref_longitude is None:
                raise ValueError("Reference location not found in configuration file")

            logger.info(f"Loaded reference location from config: ({self.config.ref_latitude:.6f}, {self.config.ref_longitude:.6f}, {self.config.ref_altitude:.3f})")

        except Exception as e:
            logger.error(f"Failed to load reference location from config: {e}")
            raise ValueError("Could not load reference location from pohang_config.json")

    def _init_coordinate_transforms(self):
        """Initialize parameters for coordinate transformations"""
        lat = np.radians(self.config.ref_latitude)
        
        # Earth model parameters
        a = self.config.earth_radius
        f = self.config.earth_flattening
        e2 = 2*f - f**2  # First eccentricity squared
        
        # Radius of curvature in meridian
        self.M = a * (1 - e2) / (1 - e2 * np.sin(lat)**2)**(3/2)
        
        # Radius of curvature in prime vertical
        self.N = a / np.sqrt(1 - e2 * np.sin(lat)**2)
        
        logger.info(f"Initialized coordinate transforms with reference: "
                   f"({self.config.ref_latitude}, {self.config.ref_longitude})")

    def get_lgf_reference_location(self) -> Tuple[float, float, float]:
        """
        Get the Local Geodetic Frame (LGF) reference location

        Returns:
            Tuple of (latitude, longitude, altitude) in degrees and meters
        """
        return (self.config.ref_latitude, self.config.ref_longitude, self.config.ref_altitude)

    def get_baseline_center_offset(self) -> Optional[np.ndarray]:
        """
        Get the baseline center offset if baseline is in local coordinates

        Returns:
            Baseline center offset [x, y, z] or None if not available
        """
        return self.baseline_center_offset

    def _find_baseline_origin_gps(self, gps_lats: np.ndarray, gps_lons: np.ndarray, gps_alts: np.ndarray) -> Optional[Tuple[float, float, float]]:
        """
        Find GPS coordinates that correspond to baseline coordinate system origin.

        Args:
            gps_lats: GPS latitude array
            gps_lons: GPS longitude array
            gps_alts: GPS altitude array

        Returns:
            Tuple of (lat, lon, alt) for LGF origin or None if not found
        """
        try:
            # Load baseline data to correlate with GPS
            baseline_file = self.dataset_path / "navigation" / "baseline.txt"
            if not baseline_file.exists():
                baseline_file = self.dataset_path / "navigation" / "baseline_sampled.txt"

            if not baseline_file.exists():
                logger.warning("No baseline file found for correlation")
                return None

            # Load baseline data
            baseline_data = np.loadtxt(baseline_file)
            if baseline_data.shape[1] < 8:
                logger.warning("Baseline file format not suitable for correlation")
                return None

            baseline_positions = baseline_data[:, 5:8]  # [x, y, z] in baseline frame
            baseline_times = baseline_data[:, 0]  # Assuming first column is time

            # Strategy 1: Find the baseline point closest to origin (0,0,0)
            baseline_distances_to_origin = np.linalg.norm(baseline_positions, axis=1)
            closest_to_origin_idx = np.argmin(baseline_distances_to_origin)

            logger.info(f"Baseline point closest to origin: {baseline_positions[closest_to_origin_idx]}")
            logger.info(f"Distance to origin: {baseline_distances_to_origin[closest_to_origin_idx]:.3f}m")

            # If we have time correlation, find corresponding GPS point
            if len(gps_lats) > 1 and len(baseline_times) > 1:
                # Find GPS point at similar time
                baseline_time_at_origin = baseline_times[closest_to_origin_idx]

                # For now, use a simple approach: GPS point that minimizes baseline coordinates
                # This assumes the baseline origin is near the center of the GPS trajectory

                # Calculate GPS trajectory center as approximation of baseline origin
                center_lat = np.mean(gps_lats)
                center_lon = np.mean(gps_lons)
                center_alt = np.mean(gps_alts)

                logger.info(f"Using GPS trajectory center as baseline origin approximation:")
                logger.info(f"  Lat: {center_lat:.6f}°, Lon: {center_lon:.6f}°, Alt: {center_alt:.3f}m")

                return (center_lat, center_lon, center_alt)
            else:
                logger.warning("Insufficient data for GPS-baseline correlation")
                return None

        except Exception as e:
            logger.error(f"Error correlating baseline origin with GPS: {e}")
            return None


    
    def load_ahrs_data(self, filepath: Optional[str] = None) -> Dict:
        """
        Load AHRS/IMU data from file
        
        File format (11 columns):
        - Unix time (s)
        - Quaternion (w, x, y, z)
        - Angular rates (rad/s) - body frame
        - Linear accelerations (m/s²) - body frame
        
        Returns:
            Dictionary with parsed AHRS data
        """
        if filepath is None:
            # Try both standard and sampled filenames
            filepath = self.dataset_path / "navigation" / "ahrs.txt"
            if not filepath.exists():
                filepath = self.dataset_path / "navigation" / "ahrs_sampled.txt"
        
        logger.info(f"Loading AHRS data from: {filepath}")
        
        try:
            # Load data
            data = np.loadtxt(filepath)

            # Apply time limits if configured
            if self.config.max_processing_duration is not None:
                start_time = data[0, 0] + self.config.start_time_offset
                end_time = start_time + self.config.max_processing_duration
                time_mask = (data[:, 0] >= start_time) & (data[:, 0] <= end_time)
                data = data[time_mask]
                logger.info(f"Applied time limit: {self.config.max_processing_duration}s "
                           f"(from {self.config.start_time_offset}s offset)")

            # Parse columns
            ahrs_data = {
                'time': data[:, 0],                    # Unix time
                'quaternion': data[:, 1:5],            # [w, x, y, z]
                'angular_velocity': data[:, 5:8],      # [wx, wy, wz] rad/s
                'acceleration': data[:, 8:11]          # [ax, ay, az] m/s²
            }
            
            # Validate quaternions
            for i in range(len(ahrs_data['quaternion'])):
                q = ahrs_data['quaternion'][i]
                norm = np.linalg.norm(q)
                if abs(norm - 1.0) > 0.01:
                    logger.warning(f"Quaternion at index {i} not normalized: norm={norm}")
                    ahrs_data['quaternion'][i] = q / norm
            
            self.ahrs_data = ahrs_data
            duration = ahrs_data['time'][-1] - ahrs_data['time'][0]
            logger.info(f"Loaded {len(ahrs_data['time'])} AHRS samples "
                       f"(duration: {duration:.1f}s)")

            return ahrs_data
            
        except Exception as e:
            logger.error(f"Failed to load AHRS data: {e}")
            raise
    
    def load_gps_data(self, filepath: Optional[str] = None) -> Dict:
        """
        Load GPS data from file
        
        File format (11 columns):
        - Unix time (s)
        - GPS time (s)
        - Latitude (degrees)
        - Hemisphere (N/S)
        - Longitude (degrees) 
        - Hemisphere (E/W)
        - Heading (degrees)
        - GPS quality (0-9)
        - Number of satellites
        - HDOP
        - Geoid height (m)
        
        Returns:
            Dictionary with parsed GPS data
        """
        if filepath is None:
            # Try both standard and sampled filenames
            filepath = self.dataset_path / "navigation" / "gps.txt"
            if not filepath.exists():
                filepath = self.dataset_path / "navigation" / "gps_sampled.txt"
        
        logger.info(f"Loading GPS data from: {filepath}")
        
        try:
            # Load data with mixed types (strings for hemispheres)
            with open(filepath, 'r') as f:
                lines = f.readlines()

            # Parse each line manually to handle string columns
            parsed_data = []
            for line in lines:
                parts = line.strip().split('\t')
                if len(parts) >= 11:
                    parsed_data.append([
                        float(parts[0]),   # Unix time
                        float(parts[1]),   # GPS time
                        float(parts[2]),   # Latitude
                        parts[3],          # Hemisphere (N/S)
                        float(parts[4]),   # Longitude
                        parts[5],          # Hemisphere (E/W)
                        float(parts[6]),   # Heading
                        int(parts[7]),     # GPS quality
                        int(parts[8]),     # Number of satellites
                        float(parts[9]),   # HDOP
                        float(parts[10])   # Geoid height
                    ])

            # Apply time limits if configured
            if self.config.max_processing_duration is not None and parsed_data:
                start_time = parsed_data[0][0] + self.config.start_time_offset
                end_time = start_time + self.config.max_processing_duration
                parsed_data = [row for row in parsed_data if start_time <= row[0] <= end_time]
                logger.info(f"Applied GPS time limit: {self.config.max_processing_duration}s "
                           f"(from {self.config.start_time_offset}s offset)")

            # Convert to arrays
            parsed_array = np.array(parsed_data, dtype=object)

            # Extract data with proper hemisphere handling
            gps_data = {
                'time': parsed_array[:, 0].astype(float),
                'gps_time': parsed_array[:, 1].astype(float),
                'latitude': parsed_array[:, 2].astype(float),
                'longitude': parsed_array[:, 4].astype(float),
                'altitude': parsed_array[:, 10].astype(float),  # geoid height as altitude
                'heading': parsed_array[:, 6].astype(float),    # degrees
                'quality': parsed_array[:, 7].astype(int),      # GPS quality indicator
                'num_satellites': parsed_array[:, 8].astype(int),
                'hdop': parsed_array[:, 9].astype(float)
            }

            # Adjust for hemisphere
            lat_hemisphere = parsed_array[:, 3]
            lon_hemisphere = parsed_array[:, 5]

            # Apply hemisphere corrections
            for i in range(len(gps_data['latitude'])):
                if lat_hemisphere[i] == 'S':
                    gps_data['latitude'][i] *= -1
                if lon_hemisphere[i] == 'W':
                    gps_data['longitude'][i] *= -1
            
            # Convert heading to radians
            gps_data['heading_rad'] = np.radians(gps_data['heading'])
            
            # Convert LLA to NED using standardized transformation
            from utils.coordinate_transforms import lla_to_ned

            lla_array = np.column_stack([
                gps_data['latitude'],
                gps_data['longitude'],
                gps_data['altitude']
            ])
            ref_lla = (self.config.ref_latitude, self.config.ref_longitude, self.config.ref_altitude)
            gps_data['position_ned'] = lla_to_ned(lla_array, ref_lla)
            
            self.gps_data = gps_data
            logger.info(f"Loaded {len(gps_data['time'])} GPS samples")
            
            return gps_data
            
        except Exception as e:
            logger.error(f"Failed to load GPS data: {e}")
            raise
    
    def load_baseline_data(self, filepath: Optional[str] = None) -> Dict:
        """
        Load baseline (ground truth) data
        
        File format (8 columns):
        - Unix time (s)
        - Quaternion (w, x, y, z)
        - Position (x, y, z) in local frame
        
        Returns:
            Dictionary with parsed baseline data
        """
        if filepath is None:
            # Try both standard and sampled filenames
            filepath = self.dataset_path / "navigation" / "baseline.txt"
            if not filepath.exists():
                filepath = self.dataset_path / "navigation" / "baseline_sampled.txt"
        
        logger.info(f"Loading baseline data from: {filepath}")
        
        try:
            # Load data
            data = np.loadtxt(filepath)

            # Apply time limits if configured
            if self.config.max_processing_duration is not None:
                start_time = data[0, 0] + self.config.start_time_offset
                end_time = start_time + self.config.max_processing_duration
                time_mask = (data[:, 0] >= start_time) & (data[:, 0] <= end_time)
                data = data[time_mask]
                logger.info(f"Applied baseline time limit: {self.config.max_processing_duration}s "
                           f"(from {self.config.start_time_offset}s offset)")

            # Parse columns
            baseline_data = {
                'time': data[:, 0],                   # Unix time
                'quaternion': data[:, 1:5],           # [w, x, y, z]
                'position': data[:, 5:8]              # [x, y, z] in local frame
            }
            
            # Baseline data is ground truth - use as-is for evaluation
            logger.info("Baseline data loaded as ground truth for evaluation")

            self.baseline_data = baseline_data
            logger.info(f"Loaded {len(baseline_data['time'])} baseline samples")
            
            return baseline_data
            
        except Exception as e:
            logger.error(f"Failed to load baseline data: {e}")
            raise
    

    
    def synchronize_data(self, target_rate: float = 100.0) -> Dict:
        """
        Synchronize all sensor data to common time grid
        
        Args:
            target_rate: Target sampling rate in Hz
            
        Returns:
            Dictionary with synchronized data
        """
        if self.ahrs_data is None or self.gps_data is None:
            raise ValueError("Must load AHRS and GPS data first")
        
        # Find common time range
        start_time = max(self.ahrs_data['time'][0], self.gps_data['time'][0])
        end_time = min(self.ahrs_data['time'][-1], self.gps_data['time'][-1])
        
        if self.baseline_data is not None:
            start_time = max(start_time, self.baseline_data['time'][0])
            end_time = min(end_time, self.baseline_data['time'][-1])
        
        # Create common time grid
        dt = 1.0 / target_rate
        common_time = np.arange(start_time, end_time, dt)
        
        logger.info(f"Synchronizing data to {target_rate} Hz, "
                   f"time range: {start_time:.3f} to {end_time:.3f}")
        
        # Interpolate AHRS data
        synced_ahrs = self._interpolate_ahrs(common_time)
        
        # Interpolate GPS data
        synced_gps = self._interpolate_gps(common_time)
        
        # Interpolate baseline if available
        synced_baseline = None
        if self.baseline_data is not None:
            synced_baseline = self._interpolate_baseline(common_time)
        
        return {
            'time': common_time,
            'ahrs': synced_ahrs,
            'gps': synced_gps,
            'baseline': synced_baseline
        }
    
    def _interpolate_ahrs(self, target_time: np.ndarray) -> Dict:
        """Interpolate AHRS data to target time grid"""
        
        # Interpolate quaternions using SLERP
        from scipy.spatial.transform import Rotation, Slerp
        
        # Convert to Rotation objects for SLERP
        rotations = Rotation.from_quat(self.ahrs_data['quaternion'][:, [1, 2, 3, 0]])  # [x,y,z,w]
        slerp = Slerp(self.ahrs_data['time'], rotations)
        interp_rotations = slerp(target_time)
        interp_quats = interp_rotations.as_quat()  # [x,y,z,w]
        
        # Convert back to [w,x,y,z] format
        quaternions = interp_quats[:, [3, 0, 1, 2]]
        
        # Interpolate other data linearly
        angular_velocity = np.zeros((len(target_time), 3))
        acceleration = np.zeros((len(target_time), 3))
        
        for i in range(3):
            f_omega = interp1d(self.ahrs_data['time'], 
                             self.ahrs_data['angular_velocity'][:, i],
                             kind=self.config.interpolation_method,
                             bounds_error=False,
                             fill_value='extrapolate')
            angular_velocity[:, i] = f_omega(target_time)
            
            f_acc = interp1d(self.ahrs_data['time'],
                           self.ahrs_data['acceleration'][:, i],
                           kind=self.config.interpolation_method,
                           bounds_error=False,
                           fill_value='extrapolate')
            acceleration[:, i] = f_acc(target_time)
        
        return {
            'quaternion': quaternions,
            'angular_velocity': angular_velocity,
            'acceleration': acceleration
        }
    
    def _interpolate_gps(self, target_time: np.ndarray) -> Dict:
        """Interpolate GPS data to target time grid"""
        
        # Find GPS measurements closest to target times
        gps_indices = []
        for t in target_time:
            idx = np.argmin(np.abs(self.gps_data['time'] - t))
            if np.abs(self.gps_data['time'][idx] - t) < self.config.time_tolerance:
                gps_indices.append(idx)
            else:
                gps_indices.append(-1)  # No GPS measurement at this time
        
        # Create GPS data at target times
        position_ned = np.full((len(target_time), 3), np.nan)
        position_lla = np.full((len(target_time), 3), np.nan)
        heading = np.full(len(target_time), np.nan)
        
        for i, idx in enumerate(gps_indices):
            if idx >= 0:
                position_ned[i] = self.gps_data['position_ned'][idx]
                position_lla[i] = [
                    self.gps_data['latitude'][idx],
                    self.gps_data['longitude'][idx],
                    self.gps_data['altitude'][idx]
                ]
                heading[i] = self.gps_data['heading_rad'][idx]
        
        return {
            'position_ned': position_ned,
            'position_lla': position_lla,
            'heading': heading,
            'available': ~np.isnan(position_ned[:, 0])  # Boolean mask for GPS availability
        }
    
    def _interpolate_baseline(self, target_time: np.ndarray) -> Dict:
        """Interpolate baseline data to target time grid"""
        
        # Similar to AHRS interpolation
        from scipy.spatial.transform import Rotation, Slerp
        
        # Quaternions with SLERP
        rotations = Rotation.from_quat(self.baseline_data['quaternion'][:, [1, 2, 3, 0]])
        slerp = Slerp(self.baseline_data['time'], rotations)
        interp_rotations = slerp(target_time)
        interp_quats = interp_rotations.as_quat()
        quaternions = interp_quats[:, [3, 0, 1, 2]]
        
        # Positions with linear interpolation
        position = np.zeros((len(target_time), 3))
        for i in range(3):
            f_pos = interp1d(self.baseline_data['time'],
                           self.baseline_data['position'][:, i],
                           kind=self.config.interpolation_method,
                           bounds_error=False,
                           fill_value='extrapolate')
            position[:, i] = f_pos(target_time)
        
        return {
            'quaternion': quaternions,
            'position': position
        }
    
    def prepare_for_fusion(self, preprocess_config: PreprocessingConfig) -> SimulationData:
        """
        Prepare data for fusion engine using original sensor timestamps, with optional preprocessing

        Args:
            preprocess_config: Configuration object for IMU preprocessing

        Returns:
            SimulationData object ready for fusion with discrete measurements
        """
        # Load all data if not already loaded
        if self.ahrs_data is None:
            self.load_ahrs_data()
        if self.gps_data is None:
            self.load_gps_data()
        # Always try to load baseline for evaluation (not for fusion ground truth)
        if self.baseline_data is None:
            try:
                self.load_baseline_data()
                logger.info("Baseline data loaded for evaluation purposes")
            except:
                logger.warning("Could not load baseline data - evaluation will be limited")
        
        # *** START: NEW INTEGRATION LOGIC ***
        ahrs_data_to_use = self.ahrs_data

        if preprocess_config.apply_preprocessing:
            logger.info("Applying Marine IMU Preprocessor...")
            # Initialize with the new config object
            preprocessor = MarineIMUPreprocessor(sampling_rate=100.0, config=preprocess_config)

            # Get raw accelerometer and gyroscope data
            raw_accel = self.ahrs_data['acceleration']
            raw_gyro = self.ahrs_data['angular_velocity']

            # Preprocess the data
            filtered_accel = preprocessor.preprocess_accelerometer(raw_accel)
            filtered_gyro = preprocessor.preprocess_gyroscope(raw_gyro)

            # Create a copy of the AHRS data to hold the filtered values
            ahrs_data_to_use = copy.deepcopy(self.ahrs_data)
            ahrs_data_to_use['acceleration'] = filtered_accel
            ahrs_data_to_use['angular_velocity'] = filtered_gyro

            logger.info("✓ IMU data preprocessed successfully.")

        # Use the (potentially filtered) data to create sensor measurements
        sensor_measurements = self._create_sensor_measurements_original(ahrs_data_to_use)
        # *** END: NEW INTEGRATION LOGIC ***

        # Create configuration
        config_dict = self._create_config_dict()

        # Use baseline as ground truth in structure (baseline IS the real ground truth)
        # But fusion engine won't use it - it only uses sensor measurements
        baseline_ground_truth = self._create_baseline_ground_truth() if self.baseline_data else None

        return SimulationData(
            ground_truth=baseline_ground_truth,
            sensor_measurements=sensor_measurements,
            configuration=config_dict
        )

    def _create_baseline_ground_truth(self) -> GroundTruth:
        """Create ground truth from baseline data (the real ground truth)"""

        if self.baseline_data is None:
            raise ValueError("Baseline data not available")

        # Use baseline data directly - this IS the real ground truth
        time = self.baseline_data['time']
        position = self.baseline_data['position']
        orientation = self.baseline_data['quaternion']

        # Calculate derivatives from baseline position
        dt = time[1] - time[0] if len(time) > 1 else 0.01
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)

        # Calculate angular velocity from quaternion changes
        angular_velocity = self._compute_angular_velocity_from_quaternions(orientation, dt)

        # Smooth derivatives
        for i in range(3):
            velocity[:, i] = self._smooth_signal(velocity[:, i])
            acceleration[:, i] = self._smooth_signal(acceleration[:, i])
            angular_velocity[:, i] = self._smooth_signal(angular_velocity[:, i])

        logger.info("Using baseline as ground truth (the real ground truth)")

        return GroundTruth(
            time=time,
            position=position,
            velocity=velocity,
            acceleration=acceleration,
            orientation=orientation,
            angular_velocity=angular_velocity
        )

    def _create_sensor_measurements_original(self, ahrs_data: Dict = None) -> SensorMeasurements:
        """Create sensor measurements with original timestamps from provided AHRS data."""

        # Use provided AHRS data or default to self.ahrs_data
        if ahrs_data is None:
            ahrs_data = self.ahrs_data

        # Time vectors - use original sensor timestamps from the provided data
        time_vectors = {
            'accelerometer': ahrs_data['time'],
            'gyroscope': ahrs_data['time'],
            'magnetometer': np.array([]),  # No magnetometer data
            'gps_position': self.gps_data['time'],
            'gps_velocity': np.array([])  # No GPS velocity in dataset
        }

        # Sensor data with original timestamps from the provided data
        accelerometer = ahrs_data['acceleration']
        gyroscope = ahrs_data['angular_velocity']

        # No magnetometer data in the dataset
        magnetometer = np.array([]).reshape(0, 3)

        # GPS position in LLA format (original timestamps)
        gps_position = np.column_stack([
            self.gps_data['latitude'],
            self.gps_data['longitude'],
            self.gps_data['altitude']
        ])

        # No GPS velocity in dataset
        gps_velocity = np.array([]).reshape(0, 3)

        return SensorMeasurements(
            time=time_vectors,
            accelerometer=accelerometer,
            gyroscope=gyroscope,
            magnetometer=magnetometer,
            gps_position=gps_position,
            gps_velocity=gps_velocity
        )

    def get_synchronized_data_for_comparison(self, target_rate: float = 50.0) -> Dict:
        """
        Get synchronized data for comparison and visualization purposes only

        Args:
            target_rate: Target sampling rate for synchronized data

        Returns:
            Dictionary with synchronized data for comparison
        """
        logger.info("Creating synchronized data for comparison/visualization purposes")
        return self.synchronize_data(target_rate=target_rate)
    

    def _create_ground_truth_from_gps(self, synced_data: Dict) -> GroundTruth:
        """Create approximate ground truth from GPS data"""
        
        time = synced_data['time']
        n_points = len(time)
        
        # Use GPS position where available, interpolate gaps
        position = synced_data['gps']['position_ned'].copy()
        
        # Fill NaN values with interpolation
        for i in range(3):
            valid_mask = ~np.isnan(position[:, i])
            if np.any(valid_mask):
                valid_indices = np.where(valid_mask)[0]
                position[~valid_mask, i] = np.interp(
                    np.where(~valid_mask)[0],
                    valid_indices,
                    position[valid_mask, i]
                )
        
        # Use AHRS orientation as it's more reliable than GPS heading
        orientation = synced_data['ahrs']['quaternion']
        
        # Calculate derivatives
        dt = time[1] - time[0] if len(time) > 1 else 0.01
        velocity = np.gradient(position, dt, axis=0)
        acceleration = np.gradient(velocity, dt, axis=0)
        
        # Use AHRS angular velocity
        angular_velocity = synced_data['ahrs']['angular_velocity']
        
        return GroundTruth(
            time=time,
            position=position,
            velocity=velocity,
            acceleration=acceleration,
            orientation=orientation,
            angular_velocity=angular_velocity
        )
    
    def _create_sensor_measurements(self, synced_data: Dict) -> SensorMeasurements:
        """Create sensor measurements structure"""
        
        # Time vectors
        time_vectors = {
            'accelerometer': synced_data['time'],
            'gyroscope': synced_data['time'],
            'magnetometer': synced_data['time'],  # Empty for now
            'gps_position': synced_data['time'][synced_data['gps']['available']],
            'gps_velocity': np.array([])  # No GPS velocity in dataset
        }
        
        # Sensor data
        accelerometer = synced_data['ahrs']['acceleration']
        gyroscope = synced_data['ahrs']['angular_velocity']
        
        # No magnetometer data in the dataset
        magnetometer = np.zeros((len(synced_data['time']), 3))
        
        # GPS position (only where available)
        gps_position = synced_data['gps']['position_lla'][synced_data['gps']['available']]
        
        # No GPS velocity in dataset
        gps_velocity = np.array([]).reshape(0, 3)
        
        return SensorMeasurements(
            time=time_vectors,
            accelerometer=accelerometer,
            gyroscope=gyroscope,
            magnetometer=magnetometer,
            gps_position=gps_position,
            gps_velocity=gps_velocity
        )
    
    def _create_config_dict(self) -> Dict:
        """Create configuration dictionary"""
        
        # Trajectory config
        traj_config = TrajectoryConfig()
        traj_config.center_lat = self.config.ref_latitude
        traj_config.center_lon = self.config.ref_longitude
        traj_config.center_alt = self.config.ref_altitude
        traj_config.trajectory_type = "COASTAL_PATROL"  # Most similar to canal navigation
        
        # Sensor config
        sensor_config = SensorConfig()
        sensor_config.imu_quality = "NAVIGATION"
        sensor_config.accelerometer_rate = 100.0
        sensor_config.gyroscope_rate = 100.0
        sensor_config.gps_position_rate = 10.0
        sensor_config.accelerometer_noise = self.config.imu_accelerometer_noise
        sensor_config.gyroscope_noise = self.config.imu_gyroscope_noise

        # Pass horizontal and vertical GPS accuracy to fusion engine
        sensor_config.gps_horizontal_accuracy = self.config.gps_horizontal_accuracy
        sensor_config.gps_vertical_accuracy = self.config.gps_vertical_accuracy
        sensor_config.use_accelerometer = True
        sensor_config.use_gyroscope = True
        sensor_config.use_magnetometer = False  # No magnetometer in dataset
        sensor_config.use_gps_position = True
        sensor_config.use_gps_velocity = False  # No GPS velocity in dataset
        
        # Environment config
        env_config = EnvironmentConfig()
        env_config.condition_template = "GOOD"
        env_config.gps_horizontal_accuracy = self.config.gps_horizontal_accuracy
        env_config.gps_vertical_accuracy = self.config.gps_vertical_accuracy
        
        return {
            'trajectory': traj_config,
            'sensors': sensor_config,
            'environment': env_config
        }
    
    def _compute_angular_velocity_from_quaternions(self, 
                                                  quaternions: np.ndarray, 
                                                  dt: float) -> np.ndarray:
        """Compute angular velocity from quaternion time series"""
        
        n_points = len(quaternions)
        angular_velocity = np.zeros((n_points, 3))
        
        for i in range(1, n_points):
            q_prev = quaternions[i-1]
            q_curr = quaternions[i]
            
            # Compute relative rotation
            r_prev = Rotation.from_quat([q_prev[1], q_prev[2], q_prev[3], q_prev[0]])
            r_curr = Rotation.from_quat([q_curr[1], q_curr[2], q_curr[3], q_curr[0]])
            
            r_rel = r_curr * r_prev.inv()
            rotvec = r_rel.as_rotvec()
            
            angular_velocity[i] = rotvec / dt
        
        # Set first point same as second
        if n_points > 1:
            angular_velocity[0] = angular_velocity[1]
        
        return angular_velocity
    
    def _smooth_signal(self, signal: np.ndarray, window_size: int = 5) -> np.ndarray:
        """Apply moving average smoothing"""
        if len(signal) < window_size:
            return signal
        
        smoothed = np.copy(signal)
        half_window = window_size // 2
        
        for i in range(half_window, len(signal) - half_window):
            smoothed[i] = np.mean(signal[i-half_window:i+half_window+1])
        
        return smoothed
    
    def get_dataset_info(self) -> Dict:
        """Get information about the loaded dataset"""
        info = {
            'dataset_path': str(self.dataset_path),
            'reference_location': {
                'latitude': self.config.ref_latitude,
                'longitude': self.config.ref_longitude,
                'altitude': self.config.ref_altitude
            }
        }
        
        if self.ahrs_data is not None:
            info['ahrs'] = {
                'samples': len(self.ahrs_data['time']),
                'duration': self.ahrs_data['time'][-1] - self.ahrs_data['time'][0],
                'rate': len(self.ahrs_data['time']) / (self.ahrs_data['time'][-1] - self.ahrs_data['time'][0])
            }
        
        if self.gps_data is not None:
            info['gps'] = {
                'samples': len(self.gps_data['time']),
                'duration': self.gps_data['time'][-1] - self.gps_data['time'][0],
                'rate': len(self.gps_data['time']) / (self.gps_data['time'][-1] - self.gps_data['time'][0])
            }
        
        if self.baseline_data is not None:
            info['baseline'] = {
                'samples': len(self.baseline_data['time']),
                'duration': self.baseline_data['time'][-1] - self.baseline_data['time'][0],
                'rate': len(self.baseline_data['time']) / (self.baseline_data['time'][-1] - self.baseline_data['time'][0])
            }
        
        return info


# Example usage function
def test_pohang_loader():
    """Test function to demonstrate usage"""
    
    # Initialize loader
    loader = PohangDatasetLoader("path/to/pohang/dataset")
    
    # Load individual data files
    loader.load_ahrs_data()
    loader.load_gps_data()
    loader.load_baseline_data()  # Optional
    
    # Get dataset info
    info = loader.get_dataset_info()
    print("Dataset Info:", info)
    
    # Prepare data for fusion
    simulation_data = loader.prepare_for_fusion(
        use_baseline_as_ground_truth=True,
        sampling_rate=100.0
    )
    
    # Now you can use this with your fusion engine
    from fusion.fusion_engine import FusionEngine
    from config.configuration_manager import SensorConfig
    
    fusion_engine = FusionEngine()
    
    # Run fusion
    sensor_config = simulation_data.configuration['sensors']
    results = fusion_engine.run_fusion(simulation_data, sensor_config)
    
    return results