"""
INS/GPS Fusion Testing Platform

A comprehensive testing platform for evaluating INS/GPS fusion algorithms
in boat motion compensation applications.

Main Components:
- Configuration management with JSON templates
- 7 trajectory types for comprehensive testing
- Complete sensor simulation with selective participation
- Integration with 28-state Extended Kalman Filter
- Comprehensive 2D/3D visualization and analysis
- Data export in multiple formats (CSV, MAT, HDF5) to ../exports/ directory

Usage:
    python main.py

Author: <PERSON><PERSON><PERSON>, PhD student at Laval Univeristy
"""

__version__ = "1.0.0"
__author__ = "INS/GPS Testing Platform"

# Import main components for convenience
from gui.main_gui import MainGUI
from config.configuration_manager import ConfigurationManager
from simulation.simulation_engine import SimulationEngine
from fusion.fusion_engine import FusionEngine
from analysis.analysis_engine import AnalysisEngine

__all__ = [
    'MainGUI',
    'ConfigurationManager', 
    'SimulationEngine',
    'FusionEngine',
    'AnalysisEngine'
]
