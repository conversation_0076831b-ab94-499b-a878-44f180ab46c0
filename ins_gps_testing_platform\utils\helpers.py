"""Helper Functions and Utilities"""
import numpy as np
import json
import os
from pathlib import Path
from datetime import datetime
from typing import Tuple, Union, Optional, Dict, Any
from dataclasses import asdict
import logging
from utils.constants import EARTH_RADIUS

logger = logging.getLogger(__name__)

def deg_to_rad(degrees: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """Convert degrees to radians"""
    return degrees * np.pi / 180.0

def rad_to_deg(radians: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """Convert radians to degrees"""
    return radians * 180.0 / np.pi

def quaternion_multiply(q1: np.ndarray, q2: np.ndarray) -> np.ndarray:
    """
    Multiply two quaternions [w, x, y, z]
    
    Args:
        q1, q2: Quaternions in [w, x, y, z] format
        
    Returns:
        Product quaternion
    """
    w1, x1, y1, z1 = q1
    w2, x2, y2, z2 = q2
    
    return np.array([
        w1*w2 - x1*x2 - y1*y2 - z1*z2,  # w
        w1*x2 + x1*w2 + y1*z2 - z1*y2,  # x
        w1*y2 - x1*z2 + y1*w2 + z1*x2,  # y
        w1*z2 + x1*y2 - y1*x2 + z1*w2   # z
    ])

def quaternion_conjugate(q: np.ndarray) -> np.ndarray:
    """
    Compute quaternion conjugate [w, -x, -y, -z]
    
    Args:
        q: Quaternion in [w, x, y, z] format
        
    Returns:
        Conjugate quaternion
    """
    return np.array([q[0], -q[1], -q[2], -q[3]])

def quaternion_to_euler(q: np.ndarray) -> Tuple[float, float, float]:
    """
    Convert quaternion to Euler angles (roll, pitch, yaw)
    
    Args:
        q: Quaternion in [w, x, y, z] format
        
    Returns:
        (roll, pitch, yaw) in radians
    """
    w, x, y, z = q
    
    # Roll (x-axis rotation)
    sinr_cosp = 2 * (w*x + y*z)
    cosr_cosp = 1 - 2 * (x*x + y*y)
    roll = np.arctan2(sinr_cosp, cosr_cosp)
    
    # Pitch (y-axis rotation)
    sinp = 2 * (w*y - z*x)
    if np.abs(sinp) >= 1:
        pitch = np.copysign(np.pi/2, sinp)  # Use 90 degrees if out of range
    else:
        pitch = np.arcsin(sinp)
    
    # Yaw (z-axis rotation)
    siny_cosp = 2 * (w*z + x*y)
    cosy_cosp = 1 - 2 * (y*y + z*z)
    yaw = np.arctan2(siny_cosp, cosy_cosp)
    
    return roll, pitch, yaw

def euler_to_quaternion(roll: float, pitch: float, yaw: float) -> np.ndarray:
    """
    Convert Euler angles to quaternion
    
    Args:
        roll, pitch, yaw: Euler angles in radians
        
    Returns:
        Quaternion in [w, x, y, z] format
    """
    cr = np.cos(roll * 0.5)
    sr = np.sin(roll * 0.5)
    cp = np.cos(pitch * 0.5)
    sp = np.sin(pitch * 0.5)
    cy = np.cos(yaw * 0.5)
    sy = np.sin(yaw * 0.5)
    
    w = cr * cp * cy + sr * sp * sy
    x = sr * cp * cy - cr * sp * sy
    y = cr * sp * cy + sr * cp * sy
    z = cr * cp * sy - sr * sp * cy
    
    return np.array([w, x, y, z])

def normalize_angle(angle: Union[float, np.ndarray]) -> Union[float, np.ndarray]:
    """Normalize angle to [-pi, pi] range"""
    return np.arctan2(np.sin(angle), np.cos(angle))

def compute_distance_2d(pos1: np.ndarray, pos2: np.ndarray) -> float:
    """Compute 2D Euclidean distance between two positions"""
    return np.linalg.norm(pos1[:2] - pos2[:2])

def compute_bearing(pos1: np.ndarray, pos2: np.ndarray) -> float:
    """
    Compute bearing from pos1 to pos2 in NED frame
    
    Args:
        pos1, pos2: Positions in [north, east, down] format
        
    Returns:
        Bearing in radians (0 = north, positive = clockwise)
    """
    delta_north = pos2[0] - pos1[0]
    delta_east = pos2[1] - pos1[1]
    
    return np.arctan2(delta_east, delta_north)

def smooth_trajectory(positions: np.ndarray, window_size: int = 5) -> np.ndarray:
    """
    Apply moving average smoothing to trajectory
    
    Args:
        positions: [N x 3] array of positions
        window_size: Size of smoothing window
        
    Returns:
        Smoothed positions
    """
    if len(positions) < window_size:
        return positions
    
    smoothed = np.copy(positions)
    half_window = window_size // 2
    
    for i in range(half_window, len(positions) - half_window):
        for j in range(3):
            smoothed[i, j] = np.mean(positions[i-half_window:i+half_window+1, j])
    
    return smoothed

def compute_curvature(positions: np.ndarray) -> np.ndarray:
    """
    Compute trajectory curvature at each point
    
    Args:
        positions: [N x 3] array of positions
        
    Returns:
        Curvature values
    """
    if len(positions) < 3:
        return np.zeros(len(positions))
    
    # First and second derivatives
    vel = np.gradient(positions, axis=0)
    acc = np.gradient(vel, axis=0)
    
    # Curvature = |v x a| / |v|^3
    curvature = np.zeros(len(positions))
    
    for i in range(len(positions)):
        v = vel[i]
        a = acc[i]
        
        v_mag = np.linalg.norm(v)
        if v_mag > 1e-6:
            cross_prod = np.cross(v, a)
            curvature[i] = np.linalg.norm(cross_prod) / (v_mag**3)
    
    return curvature

def validate_configuration(config_dict: dict) -> Tuple[bool, str]:
    """
    Validate configuration dictionary
    
    Args:
        config_dict: Configuration dictionary to validate
        
    Returns:
        (is_valid, error_message)
    """
    try:
        # Check required keys
        required_keys = ['trajectory', 'sensors', 'environment']
        for key in required_keys:
            if key not in config_dict:
                return False, f"Missing required configuration key: {key}"
        
        # Validate numeric ranges
        traj = config_dict['trajectory']
        if traj.get('duration', 0) <= 0:
            return False, "Trajectory duration must be positive"
        
        if traj.get('sampling_rate', 0) <= 0:
            return False, "Sampling rate must be positive"
        
        # Validate sensor rates
        sensors = config_dict['sensors']
        sensor_rates = [
            'accelerometer_rate', 'gyroscope_rate', 'magnetometer_rate',
            'gps_position_rate', 'gps_velocity_rate'
        ]
        
        for rate_key in sensor_rates:
            if sensors.get(rate_key, 0) <= 0:
                return False, f"Sensor rate {rate_key} must be positive"
        
        return True, "Configuration is valid"
        
    except Exception as e:
        return False, f"Configuration validation error: {str(e)}"


class NumpyEncoder(json.JSONEncoder):
    """JSON encoder for numpy arrays and data types"""
    def default(self, obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        if isinstance(obj, np.integer):
            return int(obj)
        if isinstance(obj, np.floating):
            return float(obj)
        if isinstance(obj, np.bool_):
            return bool(obj)
        return super().default(obj)


class DataPersistenceManager:
    """Manages saving and loading of simulation data and configurations"""

    def __init__(self, base_data_dir: str = "../exports"):
        self.base_data_dir = Path(base_data_dir)
        self.base_data_dir.mkdir(exist_ok=True)

    def create_scenario_folder(self, scenario_name: str) -> Path:
        """Create a timestamped folder for a simulation scenario"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        folder_name = f"{scenario_name}_{timestamp}"
        scenario_path = self.base_data_dir / folder_name
        scenario_path.mkdir(exist_ok=True)
        return scenario_path

    def save_simulation_data(self, simulation_data, scenario_path: Path,
                           filename: str = "simulation_data") -> bool:
        """Save simulation data to JSON file"""
        try:
            # Convert simulation data to serializable format
            data_dict = {
                "ground_truth": {
                    "time": simulation_data.ground_truth.time,
                    "position": simulation_data.ground_truth.position,
                    "velocity": simulation_data.ground_truth.velocity,
                    "acceleration": simulation_data.ground_truth.acceleration,
                    "orientation": simulation_data.ground_truth.orientation,
                    "angular_velocity": simulation_data.ground_truth.angular_velocity
                },
                "sensor_measurements": {
                    "time": simulation_data.sensor_measurements.time,
                    "accelerometer": simulation_data.sensor_measurements.accelerometer,
                    "gyroscope": simulation_data.sensor_measurements.gyroscope,
                    "magnetometer": simulation_data.sensor_measurements.magnetometer,
                    "gps_position": simulation_data.sensor_measurements.gps_position,
                    "gps_velocity": simulation_data.sensor_measurements.gps_velocity
                }
            }

            filepath = scenario_path / f"{filename}.json"
            with open(filepath, 'w') as f:
                json.dump(data_dict, f, cls=NumpyEncoder, indent=2)

            logger.info(f"Simulation data saved to: {filepath}")
            return True

        except Exception as e:
            logger.error(f"Failed to save simulation data: {e}")
            return False

    def save_configuration(self, config_dict: Dict[str, Any], scenario_path: Path,
                          filename: str = "configuration") -> bool:
        """Save configuration to JSON file optimized for estimation"""
        try:
            # Create estimation-optimized configuration
            estimation_config = self._create_estimation_config(config_dict)

            filepath = scenario_path / f"{filename}.json"
            with open(filepath, 'w') as f:
                json.dump(estimation_config, f, cls=NumpyEncoder, indent=2)

            logger.info(f"Estimation-optimized configuration saved to: {filepath}")
            return True

        except Exception as e:
            logger.error(f"Failed to save configuration: {e}")
            return False

    def _create_estimation_config(self, config_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Create configuration optimized for estimation by merging simulation parameters"""

        # Extract original configurations
        trajectory_config = config_dict.get('trajectory')
        sensor_config = config_dict.get('sensors')
        env_config = config_dict.get('environment')

        # Convert to dictionaries if they are dataclass objects
        if hasattr(trajectory_config, '__dict__'):
            trajectory_dict = asdict(trajectory_config)
        else:
            trajectory_dict = trajectory_config

        if hasattr(sensor_config, '__dict__'):
            sensor_dict = asdict(sensor_config)
        else:
            sensor_dict = sensor_config.copy()

        if hasattr(env_config, '__dict__'):
            env_dict = asdict(env_config)
        else:
            env_dict = env_config

        # Update sensor configuration with actual simulation noise parameters
        # GPS parameters: map environment values to sensor values (unified GPS parameters)
        if env_dict and 'gps_horizontal_accuracy' in env_dict and 'gps_vertical_accuracy' in env_dict:
            # Create 3D GPS position noise matrix [horizontal, horizontal, vertical]
            gps_position_noise_3d = [
                env_dict['gps_horizontal_accuracy']**2,  # North variance
                env_dict['gps_horizontal_accuracy']**2,  # East variance
                env_dict['gps_vertical_accuracy']**2     # Down variance
            ]
            sensor_dict['gps_position_noise_3d'] = gps_position_noise_3d

            # Keep scalar for backward compatibility (use horizontal accuracy)
            sensor_dict['gps_position_noise'] = env_dict['gps_horizontal_accuracy']

            logger.info(f"Updated GPS position noise (3D): horizontal={env_dict['gps_horizontal_accuracy']}, vertical={env_dict['gps_vertical_accuracy']}")

        if env_dict and 'gps_velocity_accuracy' in env_dict:
            sensor_dict['gps_velocity_noise'] = env_dict['gps_velocity_accuracy']
            logger.info(f"Updated GPS velocity noise: {env_dict['gps_velocity_accuracy']}")

        # Calculate gyroscope bias process noise from specifications
        if 'gyroscope_arw' in sensor_dict and 'gyroscope_bias_stability' in sensor_dict:
            from config.configuration_manager import convert_gyroscope_specs_to_noise
            measurement_noise, bias_process_noise = convert_gyroscope_specs_to_noise(
                sensor_dict['gyroscope_arw'],
                sensor_dict['gyroscope_bias_stability'],
                sensor_dict.get('gyroscope_rate', 100.0)
            )
            sensor_dict['gyroscope_bias_process_noise'] = bias_process_noise
            logger.info(f"Calculated gyroscope bias process noise: {bias_process_noise:.2e} rad/s/√s")

        # Create minimal environment config with only parameters needed for estimation
        minimal_env_dict = {}
        if env_dict:
            # Keep magnetic field parameters (needed for magnetometer fusion)
            for param in ['magnetic_declination', 'magnetic_inclination', 'magnetic_field_strength']:
                if param in env_dict:
                    minimal_env_dict[param] = env_dict[param]

            # Keep condition template for reference
            if 'condition_template' in env_dict:
                minimal_env_dict['condition_template'] = env_dict['condition_template']

        # Create the estimation-optimized configuration
        estimation_config = {
            '_metadata': {
                'description': 'Configuration optimized for estimation',
                'note': 'GPS noise parameters updated to reflect actual simulation conditions',
                'original_simulation_gps_accuracy': {
                    'horizontal': env_dict.get('gps_horizontal_accuracy') if env_dict else None,
                    'vertical': env_dict.get('gps_vertical_accuracy') if env_dict else None,
                    'velocity': env_dict.get('gps_velocity_accuracy') if env_dict else None
                }
            },
            'trajectory': trajectory_dict,
            'sensors': sensor_dict,
            'environment': minimal_env_dict
        }

        logger.info("Created estimation-optimized configuration:")
        logger.info(f"  - GPS position noise: {sensor_dict.get('gps_position_noise', 'N/A')}")
        logger.info(f"  - GPS velocity noise: {sensor_dict.get('gps_velocity_noise', 'N/A')}")
        logger.info(f"  - Environment params kept: {list(minimal_env_dict.keys())}")

        return estimation_config

    def load_simulation_data(self, filepath: Path):
        """Load simulation data from JSON file"""
        try:
            with open(filepath, 'r') as f:
                data_dict = json.load(f)

            # Convert lists back to numpy arrays
            ground_truth_data = data_dict["ground_truth"]
            sensor_data = data_dict["sensor_measurements"]

            # Convert to numpy arrays
            for key, value in ground_truth_data.items():
                ground_truth_data[key] = np.array(value)

            for key, value in sensor_data.items():
                if key == "time":
                    # Time is a dictionary of arrays
                    for sensor_name, time_array in value.items():
                        sensor_data[key][sensor_name] = np.array(time_array)
                else:
                    sensor_data[key] = np.array(value)

            logger.info(f"Simulation data loaded from: {filepath}")
            return data_dict

        except Exception as e:
            logger.error(f"Failed to load simulation data: {e}")
            return None

    def load_configuration(self, filepath: Path) -> Optional[Dict[str, Any]]:
        """Load configuration from JSON file"""
        try:
            with open(filepath, 'r') as f:
                config_dict = json.load(f)

            logger.info(f"Configuration loaded from: {filepath}")
            return config_dict

        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            return None

    def list_scenarios(self) -> list:
        """List all available scenario folders"""
        try:
            scenarios = []
            for item in self.base_data_dir.iterdir():
                if item.is_dir():
                    scenarios.append(item.name)
            return sorted(scenarios)
        except Exception as e:
            logger.error(f"Failed to list scenarios: {e}")
            return []

    def get_scenario_files(self, scenario_name: str) -> Dict[str, Optional[Path]]:
        """Get paths to data and config files for a scenario"""
        scenario_path = self.base_data_dir / scenario_name

        result = {
            "data_file": None,
            "config_file": None,
            "scenario_path": scenario_path
        }

        if not scenario_path.exists():
            return result

        # Look for data and config files
        for file_path in scenario_path.iterdir():
            if file_path.is_file() and file_path.suffix == '.json':
                if 'simulation_data' in file_path.name:
                    result["data_file"] = file_path
                elif 'configuration' in file_path.name:
                    result["config_file"] = file_path

        return result
