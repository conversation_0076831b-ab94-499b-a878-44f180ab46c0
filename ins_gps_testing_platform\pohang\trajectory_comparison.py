import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation
from typing import Dict, Tuple, Optional
import logging
import os

logger = logging.getLogger(__name__)

class TrajectoryComparator:
    """
    Compares estimated and baseline trajectories with robust alignment and time synchronization.

    This class handles:
    - Spatial alignment using SVD/Kabsch algorithm
    - Time synchronization between trajectories
    - Coordinate system detection and alignment
    - Comprehensive error analysis including position and orientation
    """

    def __init__(self):
        self.metrics = {}
        self.aligned_baseline_pos = None

    def align_and_compare(self, estimated_trajectory: Dict, baseline_trajectory: Dict) -> Dict:
        """
        Aligns and compares two trajectories.

        Args:
            estimated_trajectory: Dictionary with 'time' and 'position' of the fusion output.
            baseline_trajectory: Dictionary with 'time' and 'position' of the baseline data.

        Returns:
            A dictionary with error metrics.
        """
        est_pos = estimated_trajectory['position']
        base_pos = baseline_trajectory['position']

        # 1. Center both trajectories
        est_center = np.mean(est_pos, axis=0)
        base_center = np.mean(base_pos, axis=0)
        est_centered = est_pos - est_center
        base_centered = base_pos - base_center

        # 2. Find the optimal rotation to align the baseline to the estimate
        # Using Singular Value Decomposition (SVD) for the Kabsch algorithm
        H = base_centered.T @ est_centered
        U, _, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T

        # Handle reflection case
        if np.linalg.det(R) < 0:
            Vt[2, :] *= -1
            R = Vt.T @ U.T

        # 3. Align the baseline trajectory
        self.aligned_baseline_pos = (R @ base_centered.T).T + est_center

        # 4. Synchronize trajectories based on time
        est_time = estimated_trajectory['time']
        base_time = baseline_trajectory['time']
        
        # Simple time-based synchronization (assuming both are at the same rate)
        # For more complex cases, you might need interpolation
        max_len = min(len(est_time), len(base_time))
        est_pos_sync = est_pos[:max_len]
        aligned_baseline_pos_sync = self.aligned_baseline_pos[:max_len]
        time_sync = est_time[:max_len]

        # 5. Compute errors
        position_errors = est_pos_sync - aligned_baseline_pos_sync
        position_3d_errors = np.linalg.norm(position_errors, axis=1)

        self.metrics = {
            'rmse': np.sqrt(np.mean(position_3d_errors**2)),
            'mean': np.mean(position_3d_errors),
            'max': np.max(position_3d_errors),
            'std': np.std(position_3d_errors),
            'time_series_error': position_3d_errors,
            'time': time_sync
        }

        return self.metrics

    def get_aligned_baseline(self) -> np.ndarray:
        """Returns the aligned baseline trajectory."""
        return self.aligned_baseline_pos

    def compute_errors(self,
                      estimated_trajectory: Dict,
                      baseline_trajectory: Dict,
                      time_tolerance: float = 0.01) -> Dict:
        """
        Compute error metrics between estimated and baseline trajectories.
        This method provides compatibility with the old interface while using
        the improved alignment and synchronization.

        Args:
            estimated_trajectory: Dict with 'time', 'position', 'orientation' arrays
            baseline_trajectory: Dict with 'time', 'position', 'quaternion' arrays
            time_tolerance: Time tolerance for matching samples (not used in new method)

        Returns:
            Dictionary of error metrics compatible with old interface
        """
        # Use the improved alignment method
        self.align_and_compare(estimated_trajectory, baseline_trajectory)

        # Detect coordinate system mismatch for logging
        self._detect_coordinate_systems(estimated_trajectory, baseline_trajectory)

        # Get synchronized data
        est_pos = estimated_trajectory['position']
        est_time = estimated_trajectory['time']

        # Use aligned baseline for error computation
        max_len = min(len(est_time), len(self.aligned_baseline_pos))
        est_pos_sync = est_pos[:max_len]
        aligned_baseline_pos_sync = self.aligned_baseline_pos[:max_len]
        time_sync = est_time[:max_len]

        # Compute position errors
        position_errors = est_pos_sync - aligned_baseline_pos_sync
        position_rmse = np.sqrt(np.mean(position_errors**2, axis=0))
        position_mae = np.mean(np.abs(position_errors), axis=0)
        position_max = np.max(np.abs(position_errors), axis=0)

        # Compute total 3D errors
        position_3d_errors = np.linalg.norm(position_errors, axis=1)
        position_3d_rmse = np.sqrt(np.mean(position_3d_errors**2))
        position_3d_mae = np.mean(position_3d_errors)
        position_3d_max = np.max(position_3d_errors)

        # Compute orientation errors if available
        orientation_errors = None
        if 'orientation' in estimated_trajectory and 'quaternion' in baseline_trajectory:
            orientation_errors = self._compute_orientation_errors(
                estimated_trajectory, baseline_trajectory, max_len
            )

        # Store results in format compatible with old interface
        self.metrics = {
            'num_matched_points': max_len,
            'position': {
                'rmse': position_rmse,  # [north, east, down]
                'mae': position_mae,
                'max': position_max,
                '3d_rmse': position_3d_rmse,
                '3d_mae': position_3d_mae,
                '3d_max': position_3d_max
            },
            'time_series': {
                'time': time_sync,
                'position_errors': position_errors,
                'position_3d_errors': position_3d_errors
            }
        }

        if orientation_errors is not None:
            self.metrics['orientation'] = orientation_errors

        return self.metrics

    def _detect_coordinate_systems(self, estimated_trajectory: Dict, baseline_trajectory: Dict):
        """Detect and log coordinate system information"""

        est_pos = estimated_trajectory['position']
        base_pos = baseline_trajectory['position']

        est_range = np.abs(est_pos).max(axis=0)
        base_range = np.abs(base_pos).max(axis=0)

        logger.info("Coordinate System Analysis:")
        logger.info(f"Estimated trajectory range: N={est_range[0]:.1f}, E={est_range[1]:.1f}, D={est_range[2]:.1f}")
        logger.info(f"Baseline trajectory range: N={base_range[0]:.1f}, E={base_range[1]:.1f}, D={base_range[2]:.1f}")

        # Check if baseline is in UTM/absolute coordinates
        if base_range[0] > 100000 or base_range[1] > 100000:
            logger.warning("⚠️  COORDINATE SYSTEM MISMATCH DETECTED!")
            logger.warning("   Baseline: UTM/absolute coordinates (large values)")
            logger.warning("   Estimated: Local NED coordinates (small values)")
            logger.warning("   Large errors expected - coordinate conversion needed")
            logger.warning("   Using spatial alignment to compensate...")
            return "utm_vs_local"
        else:
            logger.info("✓ Both trajectories appear to be in local coordinates")
            return "both_local"

    def _compute_orientation_errors(self, estimated_trajectory: Dict,
                                   baseline_trajectory: Dict, max_len: int) -> Dict:
        """Compute orientation error metrics"""

        est_quat = estimated_trajectory['orientation'][:max_len]
        base_quat = baseline_trajectory['quaternion'][:max_len]

        # Compute quaternion differences
        orientation_errors = []
        for i in range(len(est_quat)):
            # Convert to Rotation objects
            r_est = Rotation.from_quat([est_quat[i, 1], est_quat[i, 2],
                                      est_quat[i, 3], est_quat[i, 0]])
            r_base = Rotation.from_quat([base_quat[i, 1], base_quat[i, 2],
                                       base_quat[i, 3], base_quat[i, 0]])

            # Compute relative rotation
            r_error = r_est * r_base.inv()

            # Convert to angle
            angle_error = r_error.magnitude()  # radians
            orientation_errors.append(angle_error)

        orientation_errors = np.array(orientation_errors)
        orientation_rmse = np.sqrt(np.mean(orientation_errors**2))
        orientation_mae = np.mean(orientation_errors)
        orientation_max = np.max(orientation_errors)

        # Store in time series for plotting
        self.metrics['time_series']['orientation_errors'] = np.degrees(orientation_errors)

        return {
            'rmse': np.degrees(orientation_rmse),  # degrees
            'mae': np.degrees(orientation_mae),
            'max': np.degrees(orientation_max)
        }

    def print_summary(self):
        """Print error statistics summary"""
        if not hasattr(self, 'metrics') or not self.metrics:
            print("No metrics computed yet. Run compute_errors first.")
            return

        print("\n" + "="*60)
        print("TRAJECTORY COMPARISON SUMMARY")
        print("="*60)

        print(f"\nMatched points: {self.metrics['num_matched_points']}")

        print("\nPosition Errors:")
        print(f"  3D RMSE: {self.metrics['position']['3d_rmse']:.3f} m")
        print(f"  3D MAE:  {self.metrics['position']['3d_mae']:.3f} m")
        print(f"  3D Max:  {self.metrics['position']['3d_max']:.3f} m")

        print("\n  Component-wise RMSE:")
        print(f"    North: {self.metrics['position']['rmse'][0]:.3f} m")
        print(f"    East:  {self.metrics['position']['rmse'][1]:.3f} m")
        print(f"    Down:  {self.metrics['position']['rmse'][2]:.3f} m")

        if 'orientation' in self.metrics:
            print("\nOrientation Errors:")
            print(f"  RMSE: {self.metrics['orientation']['rmse']:.3f}°")
            print(f"  MAE:  {self.metrics['orientation']['mae']:.3f}°")
            print(f"  Max:  {self.metrics['orientation']['max']:.3f}°")

        print("\nAlignment Method: Spatial alignment with SVD + time synchronization")
        print("="*60)

    def plot_comparison(self,
                       estimated_trajectory: Dict,
                       baseline_trajectory: Dict,
                       output_file: Optional[str] = None):
        """
        Plot trajectory comparison with enhanced visualization.
        Compatible with old interface but uses improved alignment.
        """

        if not hasattr(self, 'metrics') or not self.metrics:
            logger.warning("No metrics available. Run compute_errors first.")
            return

        fig = plt.figure(figsize=(15, 10))

        # Get data
        est_pos = estimated_trajectory['position']
        base_pos = baseline_trajectory['position']

        # 3D trajectory plot
        ax1 = fig.add_subplot(221, projection='3d')
        ax1.plot(est_pos[:, 1],  # East
                est_pos[:, 0],   # North
                -est_pos[:, 2],  # Up
                'b-', label='Estimated', linewidth=2)
        ax1.plot(base_pos[:, 1],
                base_pos[:, 0],
                -base_pos[:, 2],
                'r--', label='Baseline (Original)', linewidth=2, alpha=0.7)

        # Plot aligned baseline if available
        if self.aligned_baseline_pos is not None:
            ax1.plot(self.aligned_baseline_pos[:, 1],
                    self.aligned_baseline_pos[:, 0],
                    -self.aligned_baseline_pos[:, 2],
                    'g:', label='Baseline (Aligned)', linewidth=2)

        ax1.set_xlabel('East (m)')
        ax1.set_ylabel('North (m)')
        ax1.set_zlabel('Up (m)')
        ax1.set_title('3D Trajectory Comparison')
        ax1.legend()

        # 2D trajectory plot (top view)
        ax2 = fig.add_subplot(222)
        ax2.plot(est_pos[:, 1],
                est_pos[:, 0],
                'b-', label='Estimated', linewidth=2)
        ax2.plot(base_pos[:, 1],
                base_pos[:, 0],
                'r--', label='Baseline (Original)', linewidth=2, alpha=0.7)

        if self.aligned_baseline_pos is not None:
            ax2.plot(self.aligned_baseline_pos[:, 1],
                    self.aligned_baseline_pos[:, 0],
                    'g:', label='Baseline (Aligned)', linewidth=2)

        ax2.set_xlabel('East (m)')
        ax2.set_ylabel('North (m)')
        ax2.set_title('2D Trajectory (Top View)')
        ax2.axis('equal')
        ax2.grid(True)
        ax2.legend()

        # Error plots
        if 'time_series' in self.metrics:
            time = self.metrics['time_series']['time']
            errors = self.metrics['time_series']['position_errors']

            # Position error components
            ax3 = fig.add_subplot(223)
            ax3.plot(time, errors[:, 0], 'r-', label='North')
            ax3.plot(time, errors[:, 1], 'g-', label='East')
            ax3.plot(time, errors[:, 2], 'b-', label='Down')
            ax3.set_xlabel('Time (s)')
            ax3.set_ylabel('Position Error (m)')
            ax3.set_title('Position Error Components')
            ax3.grid(True)
            ax3.legend()

            # 3D position error
            ax4 = fig.add_subplot(224)
            ax4.plot(time, self.metrics['time_series']['position_3d_errors'], 'k-')
            ax4.set_xlabel('Time (s)')
            ax4.set_ylabel('3D Position Error (m)')
            ax4.set_title('Total Position Error')
            ax4.grid(True)

            # Add RMSE annotation
            rmse_text = f"RMSE: {self.metrics['position']['3d_rmse']:.3f} m"
            ax4.text(0.95, 0.95, rmse_text, transform=ax4.transAxes,
                    ha='right', va='top', bbox=dict(boxstyle='round', facecolor='wheat'))

        plt.tight_layout()

        if output_file:
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            logger.info(f"📊 Trajectory comparison saved to: {os.path.abspath(output_file)}")

        # Don't show plots in background thread - just save them
        plt.close()

def plot_trajectory_comparison(estimated_pos: np.ndarray,
                               baseline_pos: np.ndarray,
                               aligned_baseline_pos: np.ndarray,
                               metrics: Dict) -> plt.Figure:
    """
    Generates a comprehensive plot comparing the trajectories.
    This is a standalone function for external use.
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 14))

    # Plot 1: Original Trajectories (Before Alignment)
    ax1.plot(estimated_pos[:, 1], estimated_pos[:, 0], 'b-', label='Estimated')
    ax1.plot(baseline_pos[:, 1], baseline_pos[:, 0], 'r--', label='Baseline (Original)')
    ax1.set_title('Trajectories Before Alignment')
    ax1.set_xlabel('East (m)')
    ax1.set_ylabel('North (m)')
    ax1.legend()
    ax1.axis('equal')
    ax1.grid(True)

    # Plot 2: Aligned Trajectories
    ax2.plot(estimated_pos[:, 1], estimated_pos[:, 0], 'b-', label='Estimated')
    ax2.plot(aligned_baseline_pos[:, 1], aligned_baseline_pos[:, 0], 'g--', label='Baseline (Aligned)')
    ax2.set_title('Trajectories After Alignment')
    ax2.set_xlabel('East (m)')
    ax2.set_ylabel('North (m)')
    ax2.legend()
    ax2.axis('equal')
    ax2.grid(True)

    # Plot 3: Position Error Over Time
    ax3.plot(metrics['time'], metrics['time_series_error'], 'k-')
    ax3.set_title('Position Error Over Time')
    ax3.set_xlabel('Time (s)')
    ax3.set_ylabel('3D Position Error (m)')
    ax3.grid(True)
    stats_text = (f"RMSE: {metrics['rmse']:.3f} m\n"
                  f"Mean: {metrics['mean']:.3f} m\n"
                  f"Max: {metrics['max']:.3f} m")
    ax3.text(0.05, 0.95, stats_text, transform=ax3.transAxes, fontsize=10,
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

    # Plot 4: Error Histogram
    ax4.hist(metrics['time_series_error'], bins=50, density=True, alpha=0.7, color='purple')
    ax4.set_title('Error Distribution')
    ax4.set_xlabel('Position Error (m)')
    ax4.set_ylabel('Density')
    ax4.grid(True)

    fig.suptitle('Estimated vs. Baseline Trajectory Comparison', fontsize=16)
    fig.tight_layout(rect=[0, 0.03, 1, 0.95])

    return fig