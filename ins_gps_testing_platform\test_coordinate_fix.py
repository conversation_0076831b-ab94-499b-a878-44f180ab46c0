#!/usr/bin/env python3
"""
Test script to verify the coordinate system fix for baseline data.

This script tests whether the coordinate system mapping fix correctly
aligns GPS and baseline trajectories.
"""

import numpy as np
import matplotlib.pyplot as plt
import logging
from pathlib import Path
import sys

# Add parent directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_coordinate_system_fix():
    """Test the coordinate system fix with synthetic data"""
    
    logger.info("="*60)
    logger.info("TESTING COORDINATE SYSTEM FIX")
    logger.info("="*60)
    
    # Create a simple circular trajectory in NED coordinates
    t = np.linspace(0, 2*np.pi, 100)
    radius = 50.0
    
    # True trajectory in NED format [North, East, Down]
    true_north = radius * np.cos(t)
    true_east = radius * np.sin(t)
    true_down = -5 * np.ones_like(t)
    true_trajectory_ned = np.column_stack([true_north, true_east, true_down])
    
    # Simulate baseline data in UTM format [Easting, Northing, Down]
    # This is how baseline data is stored in the file
    baseline_easting = true_east + 500000  # Add UTM offset
    baseline_northing = true_north + 4000000  # Add UTM offset
    baseline_down = true_down
    baseline_trajectory_utm = np.column_stack([baseline_easting, baseline_northing, baseline_down])
    
    logger.info("Created test trajectories:")
    logger.info(f"  True NED: North=[{true_north.min():.1f}, {true_north.max():.1f}], East=[{true_east.min():.1f}, {true_east.max():.1f}]")
    logger.info(f"  Baseline UTM: Easting=[{baseline_easting.min():.1f}, {baseline_easting.max():.1f}], Northing=[{baseline_northing.min():.1f}, {baseline_northing.max():.1f}]")
    
    # Test OLD method (incorrect coordinate mapping)
    logger.info("\n1. Testing OLD method (incorrect mapping):")
    old_base_north = baseline_trajectory_utm[:, 0]  # ❌ Easting treated as North
    old_base_east = baseline_trajectory_utm[:, 1]   # ❌ Northing treated as East
    old_baseline_ned = np.column_stack([old_base_north, old_base_east, baseline_down])
    
    # Calculate alignment error with old method
    old_center_true = np.mean(true_trajectory_ned, axis=0)
    old_center_baseline = np.mean(old_baseline_ned, axis=0)
    old_baseline_aligned = old_baseline_ned - old_center_baseline + old_center_true
    old_errors = true_trajectory_ned - old_baseline_aligned
    old_rmse = np.sqrt(np.mean(np.sum(old_errors**2, axis=1)))
    
    logger.info(f"  Old method RMSE: {old_rmse:.1f} m")
    logger.info(f"  Old baseline center: [{old_center_baseline[0]:.1f}, {old_center_baseline[1]:.1f}, {old_center_baseline[2]:.1f}]")
    
    # Test NEW method (correct coordinate mapping)
    logger.info("\n2. Testing NEW method (correct mapping):")
    new_base_north = baseline_trajectory_utm[:, 1]  # ✅ Northing treated as North
    new_base_east = baseline_trajectory_utm[:, 0]   # ✅ Easting treated as East
    new_baseline_ned = np.column_stack([new_base_north, new_base_east, baseline_down])
    
    # Calculate alignment error with new method
    new_center_true = np.mean(true_trajectory_ned, axis=0)
    new_center_baseline = np.mean(new_baseline_ned, axis=0)
    new_baseline_aligned = new_baseline_ned - new_center_baseline + new_center_true
    new_errors = true_trajectory_ned - new_baseline_aligned
    new_rmse = np.sqrt(np.mean(np.sum(new_errors**2, axis=1)))
    
    logger.info(f"  New method RMSE: {new_rmse:.1f} m")
    logger.info(f"  New baseline center: [{new_center_baseline[0]:.1f}, {new_center_baseline[1]:.1f}, {new_center_baseline[2]:.1f}]")
    
    # Compare results
    logger.info("\n3. COMPARISON:")
    improvement = ((old_rmse - new_rmse) / old_rmse) * 100
    logger.info(f"  Old method RMSE: {old_rmse:.1f} m")
    logger.info(f"  New method RMSE: {new_rmse:.1f} m")
    logger.info(f"  Improvement: {improvement:.1f}%")
    
    if new_rmse < 1.0:  # Should be nearly zero for perfect synthetic data
        logger.info("  ✅ NEW method correctly aligns trajectories!")
    else:
        logger.info("  ❌ NEW method still has alignment issues")
    
    if old_rmse > 50:  # Should be large due to coordinate system mismatch
        logger.info("  ✅ OLD method shows expected large misalignment")
    else:
        logger.info("  ⚠️  OLD method error smaller than expected")

    # Create visualization
    create_coordinate_fix_visualization(
        true_trajectory_ned, old_baseline_aligned, new_baseline_aligned,
        old_rmse, new_rmse
    )

    return new_rmse < 1.0 and old_rmse > 50

def create_coordinate_fix_visualization(true_traj, old_aligned, new_aligned, old_rmse, new_rmse):
    """Create visualization showing the coordinate system fix"""
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('Coordinate System Fix Verification', fontsize=16)
    
    # Plot 1: True trajectory
    ax = axes[0, 0]
    ax.plot(true_traj[:, 1], true_traj[:, 0], 'b-', linewidth=3, label='True Trajectory (NED)')
    ax.set_xlabel('East (m)')
    ax.set_ylabel('North (m)')
    ax.set_title('True Trajectory (Reference)')
    ax.legend()
    ax.grid(True)
    ax.axis('equal')
    
    # Plot 2: Old method (incorrect)
    ax = axes[0, 1]
    ax.plot(true_traj[:, 1], true_traj[:, 0], 'b-', linewidth=3, label='True Trajectory', alpha=0.8)
    ax.plot(old_aligned[:, 1], old_aligned[:, 0], 'r--', linewidth=2, label='Old Method (Wrong)', alpha=0.8)
    ax.set_xlabel('East (m)')
    ax.set_ylabel('North (m)')
    ax.set_title(f'Old Method - RMSE: {old_rmse:.1f}m')
    ax.legend()
    ax.grid(True)
    ax.axis('equal')
    
    # Plot 3: New method (correct)
    ax = axes[1, 0]
    ax.plot(true_traj[:, 1], true_traj[:, 0], 'b-', linewidth=3, label='True Trajectory', alpha=0.8)
    ax.plot(new_aligned[:, 1], new_aligned[:, 0], 'g--', linewidth=2, label='New Method (Fixed)', alpha=0.8)
    ax.set_xlabel('East (m)')
    ax.set_ylabel('North (m)')
    ax.set_title(f'New Method - RMSE: {new_rmse:.1f}m')
    ax.legend()
    ax.grid(True)
    ax.axis('equal')
    
    # Plot 4: Comparison summary
    ax = axes[1, 1]
    ax.axis('off')
    
    summary_text = "Coordinate System Fix Results:\n\n"
    summary_text += f"Problem: Baseline data in UTM format\n"
    summary_text += f"  [Easting, Northing, Down]\n"
    summary_text += f"But treated as [North, East, Down]\n\n"
    summary_text += f"Old Method (Wrong):\n"
    summary_text += f"  RMSE: {old_rmse:.1f} m\n"
    summary_text += f"  Massive misalignment\n\n"
    summary_text += f"New Method (Fixed):\n"
    summary_text += f"  RMSE: {new_rmse:.1f} m\n"
    summary_text += f"  Proper alignment\n\n"
    
    improvement = ((old_rmse - new_rmse) / old_rmse) * 100
    summary_text += f"Improvement: {improvement:.1f}%"
    
    ax.text(0.05, 0.95, summary_text, transform=ax.transAxes, fontsize=12,
            verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))
    
    plt.tight_layout()
    
    # Save the plot
    output_dir = Path("../exports")
    output_dir.mkdir(exist_ok=True)
    output_file = output_dir / "coordinate_system_fix_test.png"
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    logger.info(f"📊 Coordinate fix test visualization saved to: {output_file}")
    
    plt.close()

def main():
    """Main test function"""
    try:
        success = test_coordinate_system_fix()
        
        logger.info("\n" + "="*60)
        logger.info("TEST SUMMARY")
        logger.info("="*60)
        
        if success:
            logger.info("✅ COORDINATE SYSTEM FIX VERIFIED!")
            logger.info("✅ New method correctly aligns GPS and baseline trajectories")
            logger.info("✅ The coordinate system mapping issue has been resolved")
        else:
            logger.info("❌ COORDINATE SYSTEM FIX FAILED!")
            logger.info("❌ Further investigation needed")
        
        logger.info("📊 Visualization saved to ../exports/coordinate_system_fix_test.png")
        
        return success
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
